===== QUADRUPLES =====
0: (QuadrupleType.GOTO, None, None, 24)
1: (QuadrupleType.MULT, 5000, 5000, 9000)
2: (QuadrupleType.ASSIGN, 9000, None, 5001)
3: (QuadrupleType.PRINT, None, None, 15000)
4: (QuadrupleType.PRINT, None, None, 5000)
5: (QuadrupleType.PRINT, None, None, 15001)
6: (QuadrupleType.PRINT, None, None, 5001)
7: (QuadrupleType.NEWLINE, None, None, None)
8: (QuadrupleType.ENDPROC, None, None, None)
9: (QuadrupleType.MULT, 5000, 13000, 9000)
10: (QuadrupleType.ASSIGN, 9000, None, 5001)
11: (QuadrupleType.PRINT, None, None, 15002)
12: (QuadrupleType.PRINT, None, None, 5000)
13: (QuadrupleType.PRINT, None, None, 15001)
14: (QuadrupleType.PRINT, None, None, 5001)
15: (QuadrupleType.NEWLINE, None, None, None)
16: (QuadrupleType.PRINT, None, None, 15003)
17: (QuadrupleType.<PERSON>WLINE, None, None, None)
18: (QuadrupleType.ERA, None, None, calculate_square)
19: (QuadrupleType.PARAM, 5001, None, 0)
20: (QuadrupleType.GOSUB, None, None, 1)
21: (QuadrupleType.PRINT, None, None, 15004)
22: (QuadrupleType.NEWLINE, None, None, None)
23: (QuadrupleType.ENDPROC, None, None, None)
24: (QuadrupleType.ASSIGN, 13001, None, 1000)
25: (QuadrupleType.PRINT, None, None, 15005)
26: (QuadrupleType.PRINT, None, None, 1000)
27: (QuadrupleType.NEWLINE, None, None, None)
28: (QuadrupleType.ERA, None, None, math_operations)
29: (QuadrupleType.PARAM, 1000, None, 0)
30: (QuadrupleType.GOSUB, None, None, 1)
31: (QuadrupleType.PRINT, None, None, 15006)
32: (QuadrupleType.NEWLINE, None, None, None)
33: (QuadrupleType.END, None, None, None)
