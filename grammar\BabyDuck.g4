grammar BabyDuck;

// ---------------------- Lexer Rules (Tokens) ----------------------

PROGRAM: 'program';
MAIN: 'main';
END: 'end';
VAR: 'var';
INT: 'int';
FLOAT: 'float';
VOID: 'void';
IF: 'if';
ELSE: 'else';
WHILE: 'while';
DO: 'do';
PRINT: 'print';

PLUS: '+';
MINUS: '-';
MULT: '*';
DIV: '/';
EQ: '=';
AND: '&&';
OR: '||';
SAME: '==';
NEQ: '!=';
LT: '<';
GT: '>';

LPAREN: '(';
RPAREN: ')';
LBRACE: '{';
RBRACE: '}';
COLON: ':';
SEMICOLON: ';';
COMMA: ',';
LBRACKET: '[';
RBRACKET: ']';

// Literals
CTE_INT: [0-9]+;
CTE_FLOAT: [0-9]+ '.' [0-9]+;
CTE_STRING: '"' .*? '"';

// Identifiers
ID: [a-zA-Z_] [a-zA-Z_0-9]*;

// Skip spaces and comments
WS: [ \t\r\n]+ -> skip;
LINE_COMMENT: '//' ~[\r\n]* -> skip;

// ---------------------- Parser Rules ----------------------

program: PROGRAM ID SEMICOLON vars? funcs* MAIN body END;

vars: (VAR var_decl+)+;

var_decl: ID (COMMA ID)* COLON type SEMICOLON;

type: INT | FLOAT;

funcs:
	VOID ID LPAREN param_list? RPAREN LBRACKET vars? body RBRACKET SEMICOLON;

param_list: ID COLON type (COMMA ID COLON type)*;

body: LBRACE statement* RBRACE;

statement: condition | cycle | print_stmt | assign | f_call;

assign: ID EQ expression SEMICOLON;

f_call: ID LPAREN expression_list? RPAREN SEMICOLON;

expression_list: expression (COMMA expression)*;

condition:
	IF LPAREN expression RPAREN body (ELSE body)? SEMICOLON;

cycle: WHILE LPAREN expression RPAREN DO body SEMICOLON;

print_stmt:
	PRINT LPAREN print_val (COMMA print_val)* RPAREN SEMICOLON;

print_val: expression | CTE_STRING;

expression: exp (rel_op exp)?;

rel_op: GT | LT | NEQ | SAME | AND | OR;

exp: term ((PLUS | MINUS) term)*;

term: factor ((MULT | DIV) factor)*;

factor: LPAREN expression RPAREN | (plus_minus? id_cte);

plus_minus: PLUS | MINUS;

cte: CTE_INT | CTE_FLOAT;

id_cte: ID | cte;