program complex;
var a, b, c : int;
var d : float;

void factorial(n : int) [
    var resultado, i : int;
    {
        resultado = 1;
        i = 1;
        while (i < n + 1) do {
            resultado = resultado * i;
            i = i + 1;
        };
        print("factorial(", n, "): ", resultado);

    }
];

main {
    a = 6;
    b = 10;
    c = 0;
    d = 2.5;

    factorial(a);
    print("Suma de " , "strings");
}
end