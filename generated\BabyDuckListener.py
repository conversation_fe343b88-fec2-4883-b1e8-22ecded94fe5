# Generated from BabyDuck.g4 by ANTLR 4.13.2
from antlr4 import *
if "." in __name__:
    from .BabyDuckParser import BabyDuckParser
else:
    from BabyDuckParser import BabyDuckParser

# This class defines a complete listener for a parse tree produced by Baby<PERSON>uckParser.
class BabyDuckListener(ParseTreeListener):

    # Enter a parse tree produced by BabyDuckParser#program.
    def enterProgram(self, ctx:BabyDuckParser.ProgramContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#program.
    def exitProgram(self, ctx:BabyDuckParser.ProgramContext):
        pass


    # Enter a parse tree produced by <PERSON><PERSON>uckParser#vars.
    def enterVars(self, ctx:BabyDuckParser.VarsContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#vars.
    def exitVars(self, ctx:BabyDuckParser.VarsContext):
        pass


    # Enter a parse tree produced by <PERSON><PERSON>uckParser#var_decl.
    def enterVar_decl(self, ctx:BabyDuckParser.Var_declContext):
        pass

    # Exit a parse tree produced by <PERSON><PERSON>uckParser#var_decl.
    def exitVar_decl(self, ctx:BabyDuckParser.Var_declContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#type.
    def enterType(self, ctx:BabyDuckParser.TypeContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#type.
    def exitType(self, ctx:BabyDuckParser.TypeContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#funcs.
    def enterFuncs(self, ctx:BabyDuckParser.FuncsContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#funcs.
    def exitFuncs(self, ctx:BabyDuckParser.FuncsContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#param_list.
    def enterParam_list(self, ctx:BabyDuckParser.Param_listContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#param_list.
    def exitParam_list(self, ctx:BabyDuckParser.Param_listContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#body.
    def enterBody(self, ctx:BabyDuckParser.BodyContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#body.
    def exitBody(self, ctx:BabyDuckParser.BodyContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#statement.
    def enterStatement(self, ctx:BabyDuckParser.StatementContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#statement.
    def exitStatement(self, ctx:BabyDuckParser.StatementContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#assign.
    def enterAssign(self, ctx:BabyDuckParser.AssignContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#assign.
    def exitAssign(self, ctx:BabyDuckParser.AssignContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#f_call.
    def enterF_call(self, ctx:BabyDuckParser.F_callContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#f_call.
    def exitF_call(self, ctx:BabyDuckParser.F_callContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#expression_list.
    def enterExpression_list(self, ctx:BabyDuckParser.Expression_listContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#expression_list.
    def exitExpression_list(self, ctx:BabyDuckParser.Expression_listContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#condition.
    def enterCondition(self, ctx:BabyDuckParser.ConditionContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#condition.
    def exitCondition(self, ctx:BabyDuckParser.ConditionContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#cycle.
    def enterCycle(self, ctx:BabyDuckParser.CycleContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#cycle.
    def exitCycle(self, ctx:BabyDuckParser.CycleContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#print_stmt.
    def enterPrint_stmt(self, ctx:BabyDuckParser.Print_stmtContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#print_stmt.
    def exitPrint_stmt(self, ctx:BabyDuckParser.Print_stmtContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#print_val.
    def enterPrint_val(self, ctx:BabyDuckParser.Print_valContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#print_val.
    def exitPrint_val(self, ctx:BabyDuckParser.Print_valContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#expression.
    def enterExpression(self, ctx:BabyDuckParser.ExpressionContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#expression.
    def exitExpression(self, ctx:BabyDuckParser.ExpressionContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#rel_op.
    def enterRel_op(self, ctx:BabyDuckParser.Rel_opContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#rel_op.
    def exitRel_op(self, ctx:BabyDuckParser.Rel_opContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#exp.
    def enterExp(self, ctx:BabyDuckParser.ExpContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#exp.
    def exitExp(self, ctx:BabyDuckParser.ExpContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#term.
    def enterTerm(self, ctx:BabyDuckParser.TermContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#term.
    def exitTerm(self, ctx:BabyDuckParser.TermContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#factor.
    def enterFactor(self, ctx:BabyDuckParser.FactorContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#factor.
    def exitFactor(self, ctx:BabyDuckParser.FactorContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#plus_minus.
    def enterPlus_minus(self, ctx:BabyDuckParser.Plus_minusContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#plus_minus.
    def exitPlus_minus(self, ctx:BabyDuckParser.Plus_minusContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#cte.
    def enterCte(self, ctx:BabyDuckParser.CteContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#cte.
    def exitCte(self, ctx:BabyDuckParser.CteContext):
        pass


    # Enter a parse tree produced by BabyDuckParser#id_cte.
    def enterId_cte(self, ctx:BabyDuckParser.Id_cteContext):
        pass

    # Exit a parse tree produced by BabyDuckParser#id_cte.
    def exitId_cte(self, ctx:BabyDuckParser.Id_cteContext):
        pass



del BabyDuckParser