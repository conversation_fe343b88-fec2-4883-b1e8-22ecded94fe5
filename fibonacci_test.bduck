program fi<PERSON><PERSON><PERSON>;
var n : int;

void secuencia<PERSON><PERSON>onacci(num : int) [
    var a, b, temp, i : int;
    {
        a = 0;
        b = 1;
        i = 0;

        if (num < 1) {
            print("<PERSON><PERSON><PERSON><PERSON>(0): ", a);
        } else {
            if (num < 2) {
                print("<PERSON>bonacci(1): ", b);
            } else {
                print("Secuencia-Fibonacci-hasta: ", num);
                print("Fibonacci(0): ", a);
                print("Fibonacci(1): ", b);

                i = 2;
                while (i < num + 1) do {
                    temp = a + b;
                    print("Fibonacci(", i, "): ", temp);
                    a = b;
                    b = temp;
                    i = i + 1;
                };
            };
        };
    }
];

main {
    n = 8;
    print("Calculando scuencia Fibonacci para ", n);
    secuenciaFibonacci(n);
}
end