===== QUADRUPLES =====
0: (QuadrupleType.GOTO, None, None, 42)
1: (QuadrupleType.ASSIGN, 13000, None, 5000)
2: (QuadrupleType.ASSIGN, 13001, None, 5001)
3: (QuadrupleType.ASSIGN, 13000, None, 5002)
4: (QuadrupleType.LESS_THAN, 5003, 13001, 9000)
5: (QuadrupleType.GOTOF, 9000, None, 10)
6: (QuadrupleType.PRINT, None, None, 15000)
7: (QuadrupleType.PRINT, None, None, 5000)
8: (QuadrupleType.NEWLIN<PERSON>, None, None, None)
9: (QuadrupleType.GOTO, None, None, 41)
10: (QuadrupleType.LESS_THAN, 5003, 13002, 9001)
11: (QuadrupleType.GOTOF, 9001, None, 16)
12: (QuadrupleType.PRINT, None, None, 15001)
13: (QuadrupleType.PRINT, None, None, 5001)
14: (QuadrupleType.<PERSON><PERSON><PERSON><PERSON><PERSON>, None, None, None)
15: (QuadrupleType.GO<PERSON>, None, None, 41)
16: (QuadrupleType.PRINT, None, None, 15002)
17: (QuadrupleType.PRINT, None, None, 5003)
18: (QuadrupleType.NEWLINE, None, None, None)
19: (QuadrupleType.PRINT, None, None, 15000)
20: (QuadrupleType.PRINT, None, None, 5000)
21: (QuadrupleType.NEWLINE, None, None, None)
22: (QuadrupleType.PRINT, None, None, 15001)
23: (QuadrupleType.PRINT, None, None, 5001)
24: (QuadrupleType.NEWLINE, None, None, None)
25: (QuadrupleType.ASSIGN, 13002, None, 5002)
26: (QuadrupleType.PLUS, 5003, 13001, 9002)
27: (QuadrupleType.LESS_THAN, 5002, 9002, 9003)
28: (QuadrupleType.GOTOF, 9003, None, 41)
29: (QuadrupleType.PLUS, 5000, 5001, 9004)
30: (QuadrupleType.ASSIGN, 9004, None, 5004)
31: (QuadrupleType.PRINT, None, None, 15003)
32: (QuadrupleType.PRINT, None, None, 5002)
33: (QuadrupleType.PRINT, None, None, 15004)
34: (QuadrupleType.PRINT, None, None, 5004)
35: (QuadrupleType.NEWLINE, None, None, None)
36: (QuadrupleType.ASSIGN, 5001, None, 5000)
37: (QuadrupleType.ASSIGN, 5004, None, 5001)
38: (QuadrupleType.PLUS, 5002, 13001, 9005)
39: (QuadrupleType.ASSIGN, 9005, None, 5002)
40: (QuadrupleType.GOTO, None, None, 26)
41: (QuadrupleType.ENDPROC, None, None, None)
42: (QuadrupleType.ASSIGN, 13003, None, 1000)
43: (QuadrupleType.PRINT, None, None, 15005)
44: (QuadrupleType.PRINT, None, None, 1000)
45: (QuadrupleType.NEWLINE, None, None, None)
46: (QuadrupleType.ERA, None, None, secuenciaFibonacci)
47: (QuadrupleType.PARAM, 1000, None, 0)
48: (QuadrupleType.GOSUB, None, None, 1)
49: (QuadrupleType.END, None, None, None)
