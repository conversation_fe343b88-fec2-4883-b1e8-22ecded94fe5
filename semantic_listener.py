"""
BabyDuck Semantic Listener
-------------------------
Extiende la clase BabyDuckListener generada por ANTLR para implementar el análisis semántico con puntos neurálgicos.
"""
from antlr4 import *
from generated.BabyDuckListener import BabyDuck<PERSON>istener
from generated.BabyDuckParser import Baby<PERSON>uck<PERSON>arser
from semantic_analyzer import Type, Operator, SemanticAnalyzer
from code_generator import CodeGenerator, QuadrupleType
from debug_utils import SemanticTablePrinter

class BabyDuckSemanticListener(BabyDuckListener):
    def __init__(self, quiet=False):
        self.analyzer = SemanticAnalyzer(quiet=quiet)
        # Inicializamos el generador de codigo en None, se inicializa en enterProgram
        self.code_generator = None

        # Stack de tipos para expresiones
        self.exp_types = []
        # Stack de operadores para expresiones
        self.op_stack = []
        # Estado para saber si estamos en el alcance global
        self.in_global_scope = True
        # Nombre del programa para validaciones
        self.program_name = None
        # Quiet mode to suppress output
        self.quiet = quiet

    # ===== Helper methods =====

    def push_type(self, type_value):
        """Push a type onto the expression type stack."""
        self.exp_types.append(type_value)

    def pop_type(self):
        """Pop a type from the expression type stack."""
        if self.exp_types:
            return self.exp_types.pop()
        return Type.ERROR

    def peek_type(self):
        """Peek at the top type without popping."""
        if self.exp_types:
            return self.exp_types[-1]
        return Type.ERROR

    def push_op(self, op):
        """Push an operator onto the operation stack."""
        self.op_stack.append(op)

    def pop_op(self):
        """Pop an operator from the operation stack."""
        if self.op_stack:
            return self.op_stack.pop()
        return None

    # ===== Program structure =====

    def enterProgram(self, ctx:BabyDuckParser.ProgramContext):
        """Enter the program - set up global scope and initialize code generator."""
        # Extraemos el nombre del programa y lo agregamos como una funcion global
        self.program_name = ctx.ID().getText()
        self.analyzer.function_directory.add_function(self.program_name, Type.VOID)
        self.analyzer.function_directory.reset_current_function()  # Set to global scope

        # Inicializamos el generador de codigo con el directorio de funciones y el cubo semantico
        self.code_generator = CodeGenerator(self.analyzer.function_directory, self.analyzer.semantic_cube)

        # Generamos el GOTO para saltar las definiciones de funciones
        if self.code_generator:
            self.main_goto_index = self.code_generator.generate_quadruple(QuadrupleType.GOTO, None, None, None)

    def exitProgram(self, ctx:BabyDuckParser.ProgramContext):
        """Salimos del programa - validaciones finales y generación de código."""
        # Hacemos las validaciones semanticas finales

        # 1. Checamos que todas las funciones que no son void tengan declaraciones de retorno
        self.analyzer.validate_return_statements()

        # 2. REvisamos que todas las variables esten inicializadas
        self.analyzer.check_uninitialized_variables()

        # Generamos el cuadruplo END
        if not self.analyzer.has_errors():
            self.code_generator.end_program()

        # Imprimimos las tablas de cuadruplos para debugeo solo si no estamos en modo silencioso
        if not self.quiet:
            self.analyzer.print_tables()

            printer = SemanticTablePrinter()
            printer.print_function_directory(self.analyzer.function_directory)
            printer.print_semantic_cube(self.analyzer.semantic_cube)

            # Guardamos las tablas para visualización externa
            printer.save_tables_to_file(self.analyzer.function_directory, quiet=self.quiet)

            # Imprimimos los cuadruplos generados si no hubo errores
            if not self.analyzer.has_errors():
                self.code_generator.print_quadruples()

            # Reportamos si hubo errores
            if self.analyzer.has_errors():
                print("\n⚠️ Semantic analysis completed with errors. Code generation may not be possible.")
            else:
                print("\n✅ Semantic analysis completed successfully. Code generation completed.")
        else:
            # En modo silencioso, solo guardamos las tablas sin imprimir
            printer = SemanticTablePrinter()
            printer.save_tables_to_file(self.analyzer.function_directory, quiet=self.quiet)

    # ===== Variable declarations =====

    def enterVars(self, ctx:BabyDuckParser.VarsContext):
        """Bloque de declaración de variables."""
        # Nothing specific to do here, just a container for var_decl
        pass

    def enterVar_decl(self, ctx:BabyDuckParser.Var_declContext):
        """Procesa la declaración de variables."""
        # Obtenemos el contexto del tipo
        type_ctx = ctx.type_()

        # Mapeamos el tipo de la variable
        if type_ctx.INT():
            self.analyzer.current_type = Type.INT
        elif type_ctx.FLOAT():
            self.analyzer.current_type = Type.FLOAT
        else:
            self.analyzer.add_error(f"Invalid type at line {ctx.start.line}")
            self.analyzer.current_type = Type.ERROR

        # Procesamos todos los ids en esta declaración
        for id_token in ctx.ID():
            var_name = id_token.getText()
            # Buscamos si la variable ya existe en este alcance
            if self.analyzer.check_duplicate_variable(var_name, ctx.start.line):
                # Agregamo su entrada a la tabla de variables
                self.analyzer.function_directory.add_variable(var_name, self.analyzer.current_type)

    # ===== Function declarations =====

    def enterFuncs(self, ctx:BabyDuckParser.FuncsContext):
        """Procesamos la declaración de funciones y generamos cuadruplos."""
        # Determinamos el tipo de retorno
        if ctx.VOID():
            return_type = Type.VOID
        else:
            type_ctx = ctx.type_()
            if type_ctx.INT():
                return_type = Type.INT
            elif type_ctx.FLOAT():
                return_type = Type.FLOAT
            else:
                self.analyzer.add_error(f"Invalid return type at line {ctx.start.line}")
                return_type = Type.ERROR

        # Obtenemos el nombre de la funcion
        func_name = ctx.ID().getText()

        # Revisamos si la funcion ya existe
        if self.analyzer.check_duplicate_function(func_name, ctx.start.line):
            # Agregamos la funcion al directorio
            self.analyzer.function_directory.add_function(func_name, return_type)

            # Ponemos el scope a la funcion actual
            self.analyzer.function_directory.set_current_function(func_name)
            self.in_global_scope = False

            # Damos el contexto de la funcion a la generación de código
            if self.code_generator:
                self.code_generator.set_function_context(func_name)

    def exitFuncs(self, ctx:BabyDuckParser.FuncsContext):
        """Declaracion de fin de funcion, generamos cuadruplo de fin de funcion."""
        # generamos el cuadruplo de ENDPROC
        if self.code_generator:
            self.code_generator.end_function()

        # Reseteamos al alcance global
        self.analyzer.function_directory.reset_current_function()
        self.in_global_scope = True

    def enterParam_list(self, ctx:BabyDuckParser.Param_listContext):
        """Procesamos la lista de parametros de una funcion."""
        # Obtenemos los nombres de los parametros
        ids = [id_token.getText() for id_token in ctx.ID()]

        # Obtenemos todos los tipos de los parametros
        types = []
        for type_ctx in ctx.type_():
            if type_ctx.INT():
                types.append(Type.INT)
            elif type_ctx.FLOAT():
                types.append(Type.FLOAT)
            else:
                self.analyzer.add_error(f"Invalid parameter type at line {ctx.start.line}")
                types.append(Type.ERROR)

        # Agregamos cada parametro a la funcion actual
        for i, (param_name, param_type) in enumerate(zip(ids, types)):
            if not self.analyzer.function_directory.add_param(param_name, param_type):
                self.analyzer.add_error(f"Error adding parameter '{param_name}' at line {ctx.start.line}")

    def enterBody(self, ctx:BabyDuckParser.BodyContext):
        """ENtramos al body, checamos si es el body del programa principal o de un else."""
        # Revisa si el padre es program
        parent = ctx.parentCtx
        if isinstance(parent, BabyDuckParser.ProgramContext):
            # Si este es ek body del programa principal, ponemos el scope a la funcion principal
            self.analyzer.function_directory.set_current_function(self.program_name)
            self.in_global_scope = False

            # Ponemos el contexto de la funcion principal a la generación de código
            if self.code_generator:
                self.code_generator.set_function_context(self.program_name)

                # Llenamos el goto para saltar las definiciones de funciones
                if hasattr(self, 'main_goto_index'):
                    self.code_generator.quadruples[self.main_goto_index].result = self.code_generator.quad_counter
        elif isinstance(parent, BabyDuckParser.ConditionContext):
            # Verificamos si este es el segundo body (else) de una condicion
            condition_parent = parent
            if condition_parent.ELSE() and len(condition_parent.body()) == 2:
                # Este es el body del else, necesitamos llamar mid_if
                body_index = condition_parent.body().index(ctx)
                if body_index == 1:  # Segundo body = else body
                    if self.code_generator:
                        self.code_generator.mid_if()

    def exitBody(self, ctx:BabyDuckParser.BodyContext):
        """Salimos del body si es el body del programa principal."""
        # revisamos si program es padre
        parent = ctx.parentCtx
        if isinstance(parent, BabyDuckParser.ProgramContext):
            # Este es el body del programa principal
            # No generamos ENDPROC porque no es una funcion y se generara end.

            # Reseteamos al alcance global
            self.analyzer.function_directory.reset_current_function()
            self.in_global_scope = True

    # ===== Statements =====

    def enterAssign(self, ctx:BabyDuckParser.AssignContext):
        """Procesamos asignaciones."""
        # Variable a asignar
        var_name = ctx.ID().getText()
        var_entry = self.analyzer.check_variable_exists(var_name, ctx.start.line)

        if not var_entry:
            # Pusheamos un tipo de error para que no pete el pop en el exit
            self.push_type(Type.ERROR)
        else:
            # Pusheamos el tipo de la variable para checar compatibilidad de tipos
            self.push_type(var_entry.type)

            # Pusheamos el operador de asignacion para la generación de código
            if self.code_generator:
                self.code_generator.push_operator(QuadrupleType.ASSIGN)

    def exitAssign(self, ctx:BabyDuckParser.AssignContext):
        """Validamos tipos y generamos cuadruplo de asignación."""
        # Obtenemos tipo de la expresion (right side)
        expr_type = self.pop_type()
        # Obtenemos tipo de la variable (left side)
        var_type = self.pop_type()

        # Revisamos compatibilidad de tipos con el cubo semantico
        result_type = self.analyzer.check_operation(var_type, expr_type, Operator.ASSIGN)
        if result_type != Type.ERROR:
            # Marcamos la variable como inicializada
            var_name = ctx.ID().getText()
            self.analyzer.mark_variable_initialized(var_name)

            # Generamos cuadruplo de asignación
            if self.code_generator:
                self.code_generator.generate_assignment(var_name)

    def enterF_call(self, ctx:BabyDuckParser.F_callContext):
        """Procesamos llamadas a funciones."""
        func_name = ctx.ID().getText()
        func_entry = self.analyzer.check_function_exists(func_name, ctx.start.line)

        if not func_entry:
            return

        # Preparamos la llamada a la funcion
        if self.code_generator:
            # Generamos cuadruplo ERA (Expansion of Runtime Area)
            self.code_generator.generate_quadruple(QuadrupleType.ERA, None, None, func_name)

        # Procesamos parametros - Validamos cuenta de parametros
        param_count = 0
        if ctx.expression_list():
            param_count = len(ctx.expression_list().expression())

        # Por ahora solo se valida la cuenta de parametros, no los tipos (tipos se validan durante la evaluacion de la expresion)
        func_entry = self.analyzer.function_directory.lookup_function(func_name)
        if func_entry and len(func_entry.params) != param_count:
            self.analyzer.add_error(f"Function '{func_name}' called with incorrect number of parameters at line {ctx.start.line}. Expected {len(func_entry.params)}, got {param_count}")

    def exitF_call(self, ctx:BabyDuckParser.F_callContext):
        """Completamos el proceso de llamada a funcion."""
        func_name = ctx.ID().getText()
        func_entry = self.analyzer.check_function_exists(func_name, ctx.start.line)

        if not func_entry:
            return

        if self.code_generator:
            # Count parameters
            param_count = 0
            if ctx.expression_list():
                param_count = len(ctx.expression_list().expression())

            # Generamos el cuadruplo PARAM para cada parametro
            # HAcemos POP a los operandos para obtener los valores de los parametros en orden reverso (El stack es LIFO)
            param_values = []
            for i in range(param_count):
                param_value, param_type = self.code_generator.pop_operand()
                param_values.append(param_value)

            # Reverseamos la lista para obtener los parametros en el orden correcto
            param_values.reverse()

            # Generamos los cuadruplos PARAM
            for i, param_value in enumerate(param_values):
                self.code_generator.generate_quadruple(QuadrupleType.PARAM, param_value, None, i)

            # Obtenemos la dirección de inicio de la funcion - buscamos el primer cuadruplo de la funcion
            func_start = 1  # las funciones inician despues del GOTO inicial
            for i, quad in enumerate(self.code_generator.quadruples):
                if quad.operator == QuadrupleType.ENDPROC:
                    # La funcion inicia justo despues del ENDPROC de la funcion anterior
                    func_start = 1
                    break

            # Generamos el cuadruplo GOSUB
            self.code_generator.generate_quadruple(QuadrupleType.GOSUB, None, None, func_start)

    # ===== Conditional statements =====

    def enterCondition(self, ctx:BabyDuckParser.ConditionContext):
        """Procesamos la condicion de un if."""
        # El proceso de la condicion se maneja despues de la expresion
        pass

    def exitCondition(self, ctx:BabyDuckParser.ConditionContext):
        """Completamos el proceso de la condicion."""
        # Si hay un else, se maneja de manera distinta
        if ctx.ELSE():
            # Este es un if-else, necesitamos end_if para llenar el GOTO final
            if self.code_generator:
                self.code_generator.end_if()
        else:
            # Este es solo un if, asi que necesitamos llenar el jump del start_if
            if self.code_generator:
                self.code_generator.end_if()





    # ===== Loop statements =====

    def enterCycle(self, ctx:BabyDuckParser.CycleContext):
        """Procesamos el inicio de un ciclo while."""
        # Mark the start of the while loop
        if self.code_generator:
            self.code_generator.start_while()

    def exitCycle(self, ctx:BabyDuckParser.CycleContext):
        """Completamos el proceso de un ciclo while."""
        # Generate the jump back to the condition and fill in the exit jump
        if self.code_generator:
            self.code_generator.end_while()



    # ===== Expressions =====

    def enterExpression(self, ctx:BabyDuckParser.ExpressionContext):
        """Comenzamos a procesar una expresion."""
        # This is just an entry point, actual work in exitExpression
        pass

    def exitExpression(self, ctx:BabyDuckParser.ExpressionContext):
        """Procesamos el resultado de la expresion y comenzamos a generar cuadruplos."""
        # Si hay un operador relacional, procesamos la expresion como una comparacion
        if ctx.rel_op():
            # Pop the right operand type
            right_type = self.pop_type()
            # Pop the left operand type
            left_type = self.pop_type()

            # Get the operator
            rel_op_text = ctx.rel_op().getText()
            op_map = {
                '>': Operator.GT,
                '<': Operator.LT,
                '=': Operator.EQ,
                '!=': Operator.NEQ,
                '&&': Operator.AND,
                '||': Operator.OR,
                '<=': Operator.LE,
                '>=': Operator.GE,
                '==': Operator.EQ
            }

            quad_op_map = {
                '>': QuadrupleType.GREATER_THAN,
                '<': QuadrupleType.LESS_THAN,
                '=': QuadrupleType.EQUAL,
                '!=': QuadrupleType.NOT_EQUAL,
                '&&': Operator.AND,
                '||': Operator.OR,
                '<=': Operator.LE,
                '>=': Operator.GE,
                '==': Operator.EQ
            }

            operator = op_map.get(rel_op_text, None)
            quad_op = quad_op_map.get(rel_op_text, None)

            if operator:
                result_type = self.analyzer.check_operation(left_type, right_type, operator)
                # Pusheamos el tipo resultante para checar compatibilidad de tipos en asignaciones
                self.push_type(result_type)

                # Generamos cuadruplo de la comparacion
                if self.code_generator and quad_op:
                    # Pusheamos el operador de la comparacion
                    self.code_generator.push_operator(quad_op)
                    # Generamos el cuadruplo de comparacion
                    self.code_generator.generate_expression()
            else:
                self.analyzer.add_error(f"Unknown relational operator '{rel_op_text}' at line {ctx.start.line}")
                self.push_type(Type.ERROR)

        # Si esto es parte de una condicion, generamos el cuadruplo GOTOF
        parent = ctx.parentCtx
        if isinstance(parent, BabyDuckParser.ConditionContext):
            # Esta es la condicion de un if
            if self.code_generator:
                self.code_generator.start_if()
        elif isinstance(parent, BabyDuckParser.CycleContext):
            # TEsta es la condicion de un while
            if self.code_generator:
                self.code_generator.mid_while()

        # Si no hay operadores relacionales, el tipo ya esta en la pila de tipos

    def enterExp(self, ctx:BabyDuckParser.ExpContext):
        """Enter expression."""
        # Solo es un punto de entrada, el trabajo real esta en exitExp
        pass

    def exitExp(self, ctx:BabyDuckParser.ExpContext):
        """Procesamos la expresion de suma/resta y generamos cuadruplos de operaciones aritmeticas."""
        # SI hay operaciones de suma/resta
        plus_minus_ops = ctx.PLUS() + ctx.MINUS()
        if plus_minus_ops:
            # necesitamos combinar los terminos con operadores
            # comenzamos con los tipos de los terminos
            term_types = [self.pop_type() for _ in range(len(ctx.term()))]
            term_types.reverse()  # reverseamos para que esten en el orden correcto

            # Inicializamos el tipo resultante con el del primer termino
            result_type = term_types[0]

            # Si tenemos un generador de codigo, necesitamos generar cuadruplos
            if self.code_generator:
                # Los operandos ya estan en la pila del generador de codigo
                # Solo necesitamos procesar los operadores en orden

                # Pusheamos el tipo del primer termino (se habia sacado para checar tipos)
                self.push_type(term_types[0])

                # Procesamos cada operador y el siguiente termino
                for i, op_token in enumerate(plus_minus_ops):
                    # Pusheamos el tipo del siguiente termino (Se habia sacado para checar tipos)
                    self.push_type(term_types[i + 1])

                    op_text = op_token.getText()
                    quad_op = QuadrupleType.PLUS if op_text == '+' else QuadrupleType.MINUS

                    # Pusheamos el operador a la pila del generador de codigo
                    self.code_generator.push_operator(quad_op)

                    # Generamos el cuadruplo de la operacion
                    self.code_generator.generate_expression()
            else:
                # Procesamos cada operador y el siguiente termino para checar tipos
                for i, op_token in enumerate(plus_minus_ops):
                    next_term_type = term_types[i + 1]
                    op_text = op_token.getText()
                    operator = Operator.PLUS if op_text == '+' else Operator.MINUS

                    # Checamos la operacion y actualizamos el tipo resultante
                    result_type = self.analyzer.check_operation(result_type, next_term_type, operator)

                # Pusheamos el tipo resultante para checar compatibilidad de tipos en asignaciones
                self.push_type(result_type)

        # Si no hay operaciones de suma/resta, el tipo ya esta en la pila de tipos

    def enterTerm(self, ctx:BabyDuckParser.TermContext):
        """Enter term."""
        # Este solo es un punto de entrada, el trabajo real esta en exitTerm
        pass

    def exitTerm(self, ctx:BabyDuckParser.TermContext):
        """Procesamos el tipo del termino y generamos cuadruplos de operaciones de multiplicacion y division."""
        # Si hay operaciones de multiplicacion o division
        mult_div_ops = ctx.MULT() + ctx.DIV()
        if mult_div_ops:
            # Necesitamos combinar los factores con operadores
            # Iniciamos con los tipos de los factores
            factor_types = [self.pop_type() for _ in range(len(ctx.factor()))]
            factor_types.reverse()  # Reverse because we popped in reverse order

            # necesitamos inicializar el tipo resultante con el del primer factor
            result_type = factor_types[0]

            # Si tenemos un generador de codigo, necesitamos generar cuadruplos
            if self.code_generator:
                # Los operandos ya estan en la pila del generador de codigo
                # Solo necesitamos procesar los operadores en orden

                # Pusheamos el tipo del primer factor (se habia sacado para checar tipos)
                self.push_type(factor_types[0])

                # Procesamos cada operador y el siguiente factor
                for i, op_token in enumerate(mult_div_ops):
                    # Pusheamos el tipo del siguiente factor (se habia sacado para checar tipos)
                    self.push_type(factor_types[i + 1])

                    op_text = op_token.getText()
                    quad_op = QuadrupleType.MULT if op_text == '*' else QuadrupleType.DIV

                    # Pusheamos el operador a la pila del generador de codigo
                    self.code_generator.push_operator(quad_op)

                    # Generamos el cuadruplo de la operacion
                    self.code_generator.generate_expression()
            else:
                # Procesamos cada operador y el siguiente factor para checar tipos
                for i, op_token in enumerate(mult_div_ops):
                    next_factor_type = factor_types[i + 1]
                    op_text = op_token.getText()
                    operator = Operator.MULT if op_text == '*' else Operator.DIV

                    # Checamos cada operacion y actualizamos el tipo resultante
                    result_type = self.analyzer.check_operation(result_type, next_factor_type, operator)

                # Pusheampos el tipo resultante para checar compatibilidad de tipos en asignaciones
                self.push_type(result_type)

        # Sino hay operaciones, el tipo ya esta en la pila de tipos

    def enterFactor(self, ctx:BabyDuckParser.FactorContext):
        """Enter factor."""
        # Manejamos expresiones con parentesis
        pass

    def exitFactor(self, ctx:BabyDuckParser.FactorContext):
        """Procesamos el tipo del factor y generamos cuadruplos para variables y constantes."""
        if ctx.expression():
            # El tipo ya esta en la pila de tipos
            pass
        elif ctx.id_cte():
            if ctx.id_cte().ID():
                # Es un identificador
                var_name = ctx.id_cte().ID().getText()
                var_entry = self.analyzer.check_variable_exists(var_name, ctx.start.line)

                if not var_entry:
                    self.push_type(Type.ERROR)
                else:
                    # Revisamos si la variable fue declarada antes de ser usada
                    self.analyzer.check_variable_initialized(var_name, ctx.start.line)
                    self.push_type(var_entry.type)

                    # Pusheamos la variable a la pila del generador de codigo
                    if self.code_generator:
                        # Obtenemos la direccion de la variable
                        var_address = self.code_generator.get_address_for_variable(var_name)
                        if var_address:
                            self.code_generator.push_operand(var_address, var_entry.type)

            elif ctx.id_cte().cte():
                # Es una constante
                if ctx.id_cte().cte().CTE_INT():
                    int_value = int(ctx.id_cte().cte().CTE_INT().getText())
                    self.push_type(Type.INT)

                    # agregamos la constante int al generador de codigo
                    if self.code_generator:
                        const_address = self.code_generator.add_constant(int_value, Type.INT)
                        self.code_generator.push_operand(const_address, Type.INT)

                elif ctx.id_cte().cte().CTE_FLOAT():
                    float_value = float(ctx.id_cte().cte().CTE_FLOAT().getText())
                    self.push_type(Type.FLOAT)

                    # agregamos la constante float al generador de codigo
                    if self.code_generator:
                        const_address = self.code_generator.add_constant(float_value, Type.FLOAT)
                        self.code_generator.push_operand(const_address, Type.FLOAT)

                else:
                    self.analyzer.add_error(f"Unknown constant type at line {ctx.start.line}")
                    self.push_type(Type.ERROR)
        else:
            # Mas y menos unarios
            if ctx.plus_minus():
                # El tipado s e queda igual
                # TODO: Implementar en el generador de codigo, gramatica, y semantic analyzer
                pass
            else:
                self.analyzer.add_error(f"Invalid factor at line {ctx.start.line}")
                self.push_type(Type.ERROR)

    # ===== Literals =====

    def enterCte(self, ctx:BabyDuckParser.CteContext):
        """Process constantes."""
        # Los tipos son manejados en exitFactor
        pass

    # ===== Print =====

    def enterPrint_stmt(self, ctx:BabyDuckParser.Print_stmtContext):
        """Procesamos el print."""
        # Nothing specific to validate here
        pass

    def exitPrint_stmt(self, ctx:BabyDuckParser.Print_stmtContext):
        """Generamos los cuadruplos para el print."""
        # Procesamos cada valor a imprimir
        # De acuerdo al parser, print_stmt tiene una lista de print_val
        # Cada print_val es un CTE_STRING o una expression

        # Necesitamos procesar los argumentos en orden correcto
        # Las expresiones ya están en la pila del generador de código en orden reverso
        # Primero recolectamos todos los valores
        print_values = []

        for print_val_ctx in ctx.print_val():
            if print_val_ctx.CTE_STRING():
                # Es un string literal
                string_value = print_val_ctx.CTE_STRING().getText()
                # Quitamos comillas
                string_value = string_value[1:-1]
                print_values.append(('string', string_value))
            else:
                # Es una expresion - el valor está en la pila del generador de codigo
                print_values.append(('expression', None))

        # Ahora generamos los cuadruplos en el orden correcto
        # Las expresiones están en la pila en orden reverso, así que las sacamos al revés
        expression_values = []
        if self.code_generator:
            for value_type, value_data in print_values:
                if value_type == 'expression':
                    # Sacar de la pila del generador de código
                    operand, operand_type = self.code_generator.pop_operand()
                    expression_values.append((operand, operand_type))

        # Revertir la lista de expresiones para que estén en el orden correcto
        expression_values.reverse()

        # Ahora generar los cuadruplos en el orden correcto
        expr_index = 0
        for value_type, value_data in print_values:
            if value_type == 'string':
                if self.code_generator:
                    self.code_generator.generate_print(is_string=True, string_value=value_data)
            else:
                # Es una expresión
                if self.code_generator and expr_index < len(expression_values):
                    operand, operand_type = expression_values[expr_index]
                    # Empujar de vuelta a la pila y generar print
                    self.code_generator.push_operand(operand, operand_type)
                    self.code_generator.generate_print()
                    expr_index += 1

        # Agregamos un salto de linea al final del print
        if self.code_generator:
            self.code_generator.generate_newline()






