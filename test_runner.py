"""
BabyDuck Test Runner
-------------------
Comprehensive test suite for the BabyDuck compiler and virtual machine.
Tests compilation, quadruple generation, and program execution.
"""

import os
import sys
import subprocess
from virtual_machine import VirtualMachine
import json

class BabyDuckTestRunner:
    """Test runner for BabyDuck compiler and virtual machine."""
    
    def __init__(self):
        self.test_results = []
        self.vm = VirtualMachine()
        
    def run_all_tests(self):
        """Run all test cases."""
        print("=" * 60)
        print("BABYDUCK COMPILER AND VIRTUAL MACHINE TEST SUITE")
        print("=" * 60)
        
        # Test categories
        test_categories = [
            ("Arithmetic Operations", self.test_arithmetic_operations),
            ("Variable Assignments", self.test_variable_assignments),
            ("Control Flow - Conditionals", self.test_conditionals),
            ("Control Flow - Loops", self.test_loops),
            ("Nested Control Structures", self.test_nested_structures),
            ("Memory Management", self.test_memory_management),
            ("Error Handling", self.test_error_handling),
            ("Complex Programs", self.test_complex_programs)
        ]
        
        for category_name, test_function in test_categories:
            print(f"\n{'-' * 40}")
            print(f"Testing: {category_name}")
            print(f"{'-' * 40}")
            test_function()
        
        self.print_test_summary()
    
    def create_test_file(self, filename, content):
        """Create a test file with given content."""
        with open(filename, 'w') as f:
            f.write(content)
    
    def compile_and_run(self, test_name, bduck_code, expected_output=None):
        """Compile BabyDuck code and optionally run it."""
        filename = f"test_{test_name}.bduck"
        self.create_test_file(filename, bduck_code)
        
        try:
            # Compile the program
            result = subprocess.run([sys.executable, "main.py", filename], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                self.test_results.append({
                    "test": test_name,
                    "status": "COMPILATION_FAILED",
                    "error": result.stdout + result.stderr
                })
                print(f"❌ {test_name}: Compilation failed")
                return False
            
            # If expected output is provided, run the program
            if expected_output is not None:
                quad_file = f"test_{test_name}.quad"
                if os.path.exists(quad_file):
                    # Load and execute the program
                    if self.vm.load_program(quad_file):
                        # Capture output
                        import io
                        from contextlib import redirect_stdout
                        
                        output_buffer = io.StringIO()
                        with redirect_stdout(output_buffer):
                            self.vm.execute()
                        
                        actual_output = output_buffer.getvalue()
                        
                        if expected_output in actual_output:
                            self.test_results.append({
                                "test": test_name,
                                "status": "PASSED",
                                "expected": expected_output,
                                "actual": actual_output
                            })
                            print(f"✅ {test_name}: Passed")
                            return True
                        else:
                            self.test_results.append({
                                "test": test_name,
                                "status": "OUTPUT_MISMATCH",
                                "expected": expected_output,
                                "actual": actual_output
                            })
                            print(f"❌ {test_name}: Output mismatch")
                            return False
            else:
                self.test_results.append({
                    "test": test_name,
                    "status": "COMPILED_SUCCESSFULLY"
                })
                print(f"✅ {test_name}: Compiled successfully")
                return True
                
        except Exception as e:
            self.test_results.append({
                "test": test_name,
                "status": "ERROR",
                "error": str(e)
            })
            print(f"❌ {test_name}: Error - {e}")
            return False
        
        finally:
            # Clean up test files
            for ext in ['.bduck', '.quad', '.json']:
                test_file = f"test_{test_name}{ext}"
                if os.path.exists(test_file):
                    os.remove(test_file)
    
    def test_arithmetic_operations(self):
        """Test arithmetic operations."""
        
        # Test 1: Basic arithmetic
        code1 = """
program test_arithmetic;
var
    a, b, c : int;
    x, y : float;

main {
    a = 10;
    b = 5;
    c = a + b;
    print("Addition: ", c);
    
    c = a - b;
    print("Subtraction: ", c);
    
    c = a * b;
    print("Multiplication: ", c);
    
    x = 10.0;
    y = 3.0;
    x = x / y;
    print("Division: ", x);
}
end
"""
        self.compile_and_run("arithmetic_basic", code1)
        
        # Test 2: Complex expressions
        code2 = """
program test_complex_arithmetic;
var
    result : int;

main {
    result = 2 + 3 * 4 - 1;
    print("Complex expression result: ", result);
}
end
"""
        self.compile_and_run("arithmetic_complex", code2)
    
    def test_variable_assignments(self):
        """Test variable assignments and type handling."""
        
        code = """
program test_assignments;
var
    a, b : int;
    x, y : float;
    message : string;

main {
    a = 42;
    b = a;
    print("Integer assignment: ", b);
    
    x = 3.14;
    y = x;
    print("Float assignment: ", y);
    
    print("String literal test");
}
end
"""
        self.compile_and_run("assignments", code)
    
    def test_conditionals(self):
        """Test conditional statements."""
        
        code = """
program test_conditionals;
var
    a, b : int;

main {
    a = 10;
    b = 5;
    
    if (a > b) {
        print("a is greater than b");
    } else {
        print("a is not greater than b");
    };
    
    if (a = 10) {
        print("a equals 10");
    };
}
end
"""
        self.compile_and_run("conditionals", code)
    
    def test_loops(self):
        """Test loop statements."""
        
        code = """
program test_loops;
var
    i : int;

main {
    i = 1;
    while (i <= 3) do {
        print("Loop iteration: ", i);
        i = i + 1;
    };
    print("Loop completed");
}
end
"""
        self.compile_and_run("loops", code)
    
    def test_nested_structures(self):
        """Test nested control structures."""
        
        code = """
program test_nested;
var
    i, j : int;

main {
    i = 1;
    while (i <= 2) do {
        print("Outer loop: ", i);
        
        j = 1;
        while (j <= 2) do {
            print("  Inner loop: ", j);
            j = j + 1;
        };
        
        i = i + 1;
    };
}
end
"""
        self.compile_and_run("nested_structures", code)
    
    def test_memory_management(self):
        """Test memory management and addressing."""
        
        code = """
program test_memory;
var
    globals1, globals2 : int;
    float_var : float;

main {
    globals1 = 100;
    globals2 = 200;
    float_var = 3.14;
    
    print("Global int 1: ", globals1);
    print("Global int 2: ", globals2);
    print("Global float: ", float_var);
}
end
"""
        self.compile_and_run("memory_management", code)
    
    def test_error_handling(self):
        """Test error handling scenarios."""
        
        # Test division by zero (should be caught at runtime)
        code = """
program test_division_by_zero;
var
    a, b : int;
    result : float;

main {
    a = 10;
    b = 0;
    result = a / b;
    print("This should not print");
}
end
"""
        self.compile_and_run("division_by_zero", code)
    
    def test_complex_programs(self):
        """Test complex programs that combine multiple features."""
        
        code = """
program complex_test;
var
    n, factorial, i : int;

main {
    n = 5;
    factorial = 1;
    i = 1;
    
    print("Calculating factorial of ", n);
    
    while (i <= n) do {
        factorial = factorial * i;
        print("Step ", i, ": ", factorial);
        i = i + 1;
    };
    
    print("Final result: ", factorial);
}
end
"""
        self.compile_and_run("complex_factorial", code)
    
    def print_test_summary(self):
        """Print summary of all test results."""
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if result["status"] in ["PASSED", "COMPILED_SUCCESSFULLY"])
        failed = len(self.test_results) - passed
        
        print(f"Total Tests: {len(self.test_results)}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Success Rate: {(passed/len(self.test_results)*100):.1f}%")
        
        if failed > 0:
            print("\nFailed Tests:")
            for result in self.test_results:
                if result["status"] not in ["PASSED", "COMPILED_SUCCESSFULLY"]:
                    print(f"  - {result['test']}: {result['status']}")
                    if "error" in result:
                        print(f"    Error: {result['error'][:100]}...")


if __name__ == "__main__":
    runner = BabyDuckTestRunner()
    runner.run_all_tests()
