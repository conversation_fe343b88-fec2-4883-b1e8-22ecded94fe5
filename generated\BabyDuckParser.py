# Generated from BabyDuck.g4 by ANTLR 4.13.2
# encoding: utf-8
from antlr4 import *
from io import StringIO
import sys
if sys.version_info[1] > 5:
	from typing import TextIO
else:
	from typing.io import TextIO

def serializedATN():
    return [
        4,1,38,229,2,0,7,0,2,1,7,1,2,2,7,2,2,3,7,3,2,4,7,4,2,5,7,5,2,6,7,
        6,2,7,7,7,2,8,7,8,2,9,7,9,2,10,7,10,2,11,7,11,2,12,7,12,2,13,7,13,
        2,14,7,14,2,15,7,15,2,16,7,16,2,17,7,17,2,18,7,18,2,19,7,19,2,20,
        7,20,2,21,7,21,2,22,7,22,1,0,1,0,1,0,1,0,3,0,51,8,0,1,0,5,0,54,8,
        0,10,0,12,0,57,9,0,1,0,1,0,1,0,1,0,1,1,1,1,4,1,65,8,1,11,1,12,1,
        66,4,1,69,8,1,11,1,12,1,70,1,2,1,2,1,2,5,2,76,8,2,10,2,12,2,79,9,
        2,1,2,1,2,1,2,1,2,1,3,1,3,1,4,1,4,1,4,1,4,3,4,91,8,4,1,4,1,4,1,4,
        3,4,96,8,4,1,4,1,4,1,4,1,4,1,5,1,5,1,5,1,5,1,5,1,5,1,5,5,5,109,8,
        5,10,5,12,5,112,9,5,1,6,1,6,5,6,116,8,6,10,6,12,6,119,9,6,1,6,1,
        6,1,7,1,7,1,7,1,7,1,7,3,7,128,8,7,1,8,1,8,1,8,1,8,1,8,1,9,1,9,1,
        9,3,9,138,8,9,1,9,1,9,1,9,1,10,1,10,1,10,5,10,146,8,10,10,10,12,
        10,149,9,10,1,11,1,11,1,11,1,11,1,11,1,11,1,11,3,11,158,8,11,1,11,
        1,11,1,12,1,12,1,12,1,12,1,12,1,12,1,12,1,12,1,13,1,13,1,13,1,13,
        1,13,5,13,175,8,13,10,13,12,13,178,9,13,1,13,1,13,1,13,1,14,1,14,
        3,14,185,8,14,1,15,1,15,1,15,1,15,3,15,191,8,15,1,16,1,16,1,17,1,
        17,1,17,5,17,198,8,17,10,17,12,17,201,9,17,1,18,1,18,1,18,5,18,206,
        8,18,10,18,12,18,209,9,18,1,19,1,19,1,19,1,19,1,19,3,19,216,8,19,
        1,19,3,19,219,8,19,1,20,1,20,1,21,1,21,1,22,1,22,3,22,227,8,22,1,
        22,0,0,23,0,2,4,6,8,10,12,14,16,18,20,22,24,26,28,30,32,34,36,38,
        40,42,44,0,5,1,0,5,6,1,0,18,23,1,0,13,14,1,0,15,16,1,0,33,34,229,
        0,46,1,0,0,0,2,68,1,0,0,0,4,72,1,0,0,0,6,84,1,0,0,0,8,86,1,0,0,0,
        10,101,1,0,0,0,12,113,1,0,0,0,14,127,1,0,0,0,16,129,1,0,0,0,18,134,
        1,0,0,0,20,142,1,0,0,0,22,150,1,0,0,0,24,161,1,0,0,0,26,169,1,0,
        0,0,28,184,1,0,0,0,30,186,1,0,0,0,32,192,1,0,0,0,34,194,1,0,0,0,
        36,202,1,0,0,0,38,218,1,0,0,0,40,220,1,0,0,0,42,222,1,0,0,0,44,226,
        1,0,0,0,46,47,5,1,0,0,47,48,5,36,0,0,48,50,5,29,0,0,49,51,3,2,1,
        0,50,49,1,0,0,0,50,51,1,0,0,0,51,55,1,0,0,0,52,54,3,8,4,0,53,52,
        1,0,0,0,54,57,1,0,0,0,55,53,1,0,0,0,55,56,1,0,0,0,56,58,1,0,0,0,
        57,55,1,0,0,0,58,59,5,2,0,0,59,60,3,12,6,0,60,61,5,3,0,0,61,1,1,
        0,0,0,62,64,5,4,0,0,63,65,3,4,2,0,64,63,1,0,0,0,65,66,1,0,0,0,66,
        64,1,0,0,0,66,67,1,0,0,0,67,69,1,0,0,0,68,62,1,0,0,0,69,70,1,0,0,
        0,70,68,1,0,0,0,70,71,1,0,0,0,71,3,1,0,0,0,72,77,5,36,0,0,73,74,
        5,30,0,0,74,76,5,36,0,0,75,73,1,0,0,0,76,79,1,0,0,0,77,75,1,0,0,
        0,77,78,1,0,0,0,78,80,1,0,0,0,79,77,1,0,0,0,80,81,5,28,0,0,81,82,
        3,6,3,0,82,83,5,29,0,0,83,5,1,0,0,0,84,85,7,0,0,0,85,7,1,0,0,0,86,
        87,5,7,0,0,87,88,5,36,0,0,88,90,5,24,0,0,89,91,3,10,5,0,90,89,1,
        0,0,0,90,91,1,0,0,0,91,92,1,0,0,0,92,93,5,25,0,0,93,95,5,31,0,0,
        94,96,3,2,1,0,95,94,1,0,0,0,95,96,1,0,0,0,96,97,1,0,0,0,97,98,3,
        12,6,0,98,99,5,32,0,0,99,100,5,29,0,0,100,9,1,0,0,0,101,102,5,36,
        0,0,102,103,5,28,0,0,103,110,3,6,3,0,104,105,5,30,0,0,105,106,5,
        36,0,0,106,107,5,28,0,0,107,109,3,6,3,0,108,104,1,0,0,0,109,112,
        1,0,0,0,110,108,1,0,0,0,110,111,1,0,0,0,111,11,1,0,0,0,112,110,1,
        0,0,0,113,117,5,26,0,0,114,116,3,14,7,0,115,114,1,0,0,0,116,119,
        1,0,0,0,117,115,1,0,0,0,117,118,1,0,0,0,118,120,1,0,0,0,119,117,
        1,0,0,0,120,121,5,27,0,0,121,13,1,0,0,0,122,128,3,22,11,0,123,128,
        3,24,12,0,124,128,3,26,13,0,125,128,3,16,8,0,126,128,3,18,9,0,127,
        122,1,0,0,0,127,123,1,0,0,0,127,124,1,0,0,0,127,125,1,0,0,0,127,
        126,1,0,0,0,128,15,1,0,0,0,129,130,5,36,0,0,130,131,5,17,0,0,131,
        132,3,30,15,0,132,133,5,29,0,0,133,17,1,0,0,0,134,135,5,36,0,0,135,
        137,5,24,0,0,136,138,3,20,10,0,137,136,1,0,0,0,137,138,1,0,0,0,138,
        139,1,0,0,0,139,140,5,25,0,0,140,141,5,29,0,0,141,19,1,0,0,0,142,
        147,3,30,15,0,143,144,5,30,0,0,144,146,3,30,15,0,145,143,1,0,0,0,
        146,149,1,0,0,0,147,145,1,0,0,0,147,148,1,0,0,0,148,21,1,0,0,0,149,
        147,1,0,0,0,150,151,5,8,0,0,151,152,5,24,0,0,152,153,3,30,15,0,153,
        154,5,25,0,0,154,157,3,12,6,0,155,156,5,9,0,0,156,158,3,12,6,0,157,
        155,1,0,0,0,157,158,1,0,0,0,158,159,1,0,0,0,159,160,5,29,0,0,160,
        23,1,0,0,0,161,162,5,10,0,0,162,163,5,24,0,0,163,164,3,30,15,0,164,
        165,5,25,0,0,165,166,5,11,0,0,166,167,3,12,6,0,167,168,5,29,0,0,
        168,25,1,0,0,0,169,170,5,12,0,0,170,171,5,24,0,0,171,176,3,28,14,
        0,172,173,5,30,0,0,173,175,3,28,14,0,174,172,1,0,0,0,175,178,1,0,
        0,0,176,174,1,0,0,0,176,177,1,0,0,0,177,179,1,0,0,0,178,176,1,0,
        0,0,179,180,5,25,0,0,180,181,5,29,0,0,181,27,1,0,0,0,182,185,3,30,
        15,0,183,185,5,35,0,0,184,182,1,0,0,0,184,183,1,0,0,0,185,29,1,0,
        0,0,186,190,3,34,17,0,187,188,3,32,16,0,188,189,3,34,17,0,189,191,
        1,0,0,0,190,187,1,0,0,0,190,191,1,0,0,0,191,31,1,0,0,0,192,193,7,
        1,0,0,193,33,1,0,0,0,194,199,3,36,18,0,195,196,7,2,0,0,196,198,3,
        36,18,0,197,195,1,0,0,0,198,201,1,0,0,0,199,197,1,0,0,0,199,200,
        1,0,0,0,200,35,1,0,0,0,201,199,1,0,0,0,202,207,3,38,19,0,203,204,
        7,3,0,0,204,206,3,38,19,0,205,203,1,0,0,0,206,209,1,0,0,0,207,205,
        1,0,0,0,207,208,1,0,0,0,208,37,1,0,0,0,209,207,1,0,0,0,210,211,5,
        24,0,0,211,212,3,30,15,0,212,213,5,25,0,0,213,219,1,0,0,0,214,216,
        3,40,20,0,215,214,1,0,0,0,215,216,1,0,0,0,216,217,1,0,0,0,217,219,
        3,44,22,0,218,210,1,0,0,0,218,215,1,0,0,0,219,39,1,0,0,0,220,221,
        7,2,0,0,221,41,1,0,0,0,222,223,7,4,0,0,223,43,1,0,0,0,224,227,5,
        36,0,0,225,227,3,42,21,0,226,224,1,0,0,0,226,225,1,0,0,0,227,45,
        1,0,0,0,21,50,55,66,70,77,90,95,110,117,127,137,147,157,176,184,
        190,199,207,215,218,226
    ]

class BabyDuckParser ( Parser ):

    grammarFileName = "BabyDuck.g4"

    atn = ATNDeserializer().deserialize(serializedATN())

    decisionsToDFA = [ DFA(ds, i) for i, ds in enumerate(atn.decisionToState) ]

    sharedContextCache = PredictionContextCache()

    literalNames = [ "<INVALID>", "'program'", "'main'", "'end'", "'var'", 
                     "'int'", "'float'", "'void'", "'if'", "'else'", "'while'", 
                     "'do'", "'print'", "'+'", "'-'", "'*'", "'/'", "'='", 
                     "'&&'", "'||'", "'=='", "'!='", "'<'", "'>'", "'('", 
                     "')'", "'{'", "'}'", "':'", "';'", "','", "'['", "']'" ]

    symbolicNames = [ "<INVALID>", "PROGRAM", "MAIN", "END", "VAR", "INT", 
                      "FLOAT", "VOID", "IF", "ELSE", "WHILE", "DO", "PRINT", 
                      "PLUS", "MINUS", "MULT", "DIV", "EQ", "AND", "OR", 
                      "SAME", "NEQ", "LT", "GT", "LPAREN", "RPAREN", "LBRACE", 
                      "RBRACE", "COLON", "SEMICOLON", "COMMA", "LBRACKET", 
                      "RBRACKET", "CTE_INT", "CTE_FLOAT", "CTE_STRING", 
                      "ID", "WS", "LINE_COMMENT" ]

    RULE_program = 0
    RULE_vars = 1
    RULE_var_decl = 2
    RULE_type = 3
    RULE_funcs = 4
    RULE_param_list = 5
    RULE_body = 6
    RULE_statement = 7
    RULE_assign = 8
    RULE_f_call = 9
    RULE_expression_list = 10
    RULE_condition = 11
    RULE_cycle = 12
    RULE_print_stmt = 13
    RULE_print_val = 14
    RULE_expression = 15
    RULE_rel_op = 16
    RULE_exp = 17
    RULE_term = 18
    RULE_factor = 19
    RULE_plus_minus = 20
    RULE_cte = 21
    RULE_id_cte = 22

    ruleNames =  [ "program", "vars", "var_decl", "type", "funcs", "param_list", 
                   "body", "statement", "assign", "f_call", "expression_list", 
                   "condition", "cycle", "print_stmt", "print_val", "expression", 
                   "rel_op", "exp", "term", "factor", "plus_minus", "cte", 
                   "id_cte" ]

    EOF = Token.EOF
    PROGRAM=1
    MAIN=2
    END=3
    VAR=4
    INT=5
    FLOAT=6
    VOID=7
    IF=8
    ELSE=9
    WHILE=10
    DO=11
    PRINT=12
    PLUS=13
    MINUS=14
    MULT=15
    DIV=16
    EQ=17
    AND=18
    OR=19
    SAME=20
    NEQ=21
    LT=22
    GT=23
    LPAREN=24
    RPAREN=25
    LBRACE=26
    RBRACE=27
    COLON=28
    SEMICOLON=29
    COMMA=30
    LBRACKET=31
    RBRACKET=32
    CTE_INT=33
    CTE_FLOAT=34
    CTE_STRING=35
    ID=36
    WS=37
    LINE_COMMENT=38

    def __init__(self, input:TokenStream, output:TextIO = sys.stdout):
        super().__init__(input, output)
        self.checkVersion("4.13.2")
        self._interp = ParserATNSimulator(self, self.atn, self.decisionsToDFA, self.sharedContextCache)
        self._predicates = None




    class ProgramContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def PROGRAM(self):
            return self.getToken(BabyDuckParser.PROGRAM, 0)

        def ID(self):
            return self.getToken(BabyDuckParser.ID, 0)

        def SEMICOLON(self):
            return self.getToken(BabyDuckParser.SEMICOLON, 0)

        def MAIN(self):
            return self.getToken(BabyDuckParser.MAIN, 0)

        def body(self):
            return self.getTypedRuleContext(BabyDuckParser.BodyContext,0)


        def END(self):
            return self.getToken(BabyDuckParser.END, 0)

        def vars_(self):
            return self.getTypedRuleContext(BabyDuckParser.VarsContext,0)


        def funcs(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(BabyDuckParser.FuncsContext)
            else:
                return self.getTypedRuleContext(BabyDuckParser.FuncsContext,i)


        def getRuleIndex(self):
            return BabyDuckParser.RULE_program

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterProgram" ):
                listener.enterProgram(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitProgram" ):
                listener.exitProgram(self)




    def program(self):

        localctx = BabyDuckParser.ProgramContext(self, self._ctx, self.state)
        self.enterRule(localctx, 0, self.RULE_program)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 46
            self.match(BabyDuckParser.PROGRAM)
            self.state = 47
            self.match(BabyDuckParser.ID)
            self.state = 48
            self.match(BabyDuckParser.SEMICOLON)
            self.state = 50
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==4:
                self.state = 49
                self.vars_()


            self.state = 55
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==7:
                self.state = 52
                self.funcs()
                self.state = 57
                self._errHandler.sync(self)
                _la = self._input.LA(1)

            self.state = 58
            self.match(BabyDuckParser.MAIN)
            self.state = 59
            self.body()
            self.state = 60
            self.match(BabyDuckParser.END)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class VarsContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def VAR(self, i:int=None):
            if i is None:
                return self.getTokens(BabyDuckParser.VAR)
            else:
                return self.getToken(BabyDuckParser.VAR, i)

        def var_decl(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(BabyDuckParser.Var_declContext)
            else:
                return self.getTypedRuleContext(BabyDuckParser.Var_declContext,i)


        def getRuleIndex(self):
            return BabyDuckParser.RULE_vars

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterVars" ):
                listener.enterVars(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitVars" ):
                listener.exitVars(self)




    def vars_(self):

        localctx = BabyDuckParser.VarsContext(self, self._ctx, self.state)
        self.enterRule(localctx, 2, self.RULE_vars)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 68 
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while True:
                self.state = 62
                self.match(BabyDuckParser.VAR)
                self.state = 64 
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                while True:
                    self.state = 63
                    self.var_decl()
                    self.state = 66 
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)
                    if not (_la==36):
                        break

                self.state = 70 
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if not (_la==4):
                    break

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Var_declContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self, i:int=None):
            if i is None:
                return self.getTokens(BabyDuckParser.ID)
            else:
                return self.getToken(BabyDuckParser.ID, i)

        def COLON(self):
            return self.getToken(BabyDuckParser.COLON, 0)

        def type_(self):
            return self.getTypedRuleContext(BabyDuckParser.TypeContext,0)


        def SEMICOLON(self):
            return self.getToken(BabyDuckParser.SEMICOLON, 0)

        def COMMA(self, i:int=None):
            if i is None:
                return self.getTokens(BabyDuckParser.COMMA)
            else:
                return self.getToken(BabyDuckParser.COMMA, i)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_var_decl

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterVar_decl" ):
                listener.enterVar_decl(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitVar_decl" ):
                listener.exitVar_decl(self)




    def var_decl(self):

        localctx = BabyDuckParser.Var_declContext(self, self._ctx, self.state)
        self.enterRule(localctx, 4, self.RULE_var_decl)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 72
            self.match(BabyDuckParser.ID)
            self.state = 77
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==30:
                self.state = 73
                self.match(BabyDuckParser.COMMA)
                self.state = 74
                self.match(BabyDuckParser.ID)
                self.state = 79
                self._errHandler.sync(self)
                _la = self._input.LA(1)

            self.state = 80
            self.match(BabyDuckParser.COLON)
            self.state = 81
            self.type_()
            self.state = 82
            self.match(BabyDuckParser.SEMICOLON)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class TypeContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def INT(self):
            return self.getToken(BabyDuckParser.INT, 0)

        def FLOAT(self):
            return self.getToken(BabyDuckParser.FLOAT, 0)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_type

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterType" ):
                listener.enterType(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitType" ):
                listener.exitType(self)




    def type_(self):

        localctx = BabyDuckParser.TypeContext(self, self._ctx, self.state)
        self.enterRule(localctx, 6, self.RULE_type)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 84
            _la = self._input.LA(1)
            if not(_la==5 or _la==6):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class FuncsContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def VOID(self):
            return self.getToken(BabyDuckParser.VOID, 0)

        def ID(self):
            return self.getToken(BabyDuckParser.ID, 0)

        def LPAREN(self):
            return self.getToken(BabyDuckParser.LPAREN, 0)

        def RPAREN(self):
            return self.getToken(BabyDuckParser.RPAREN, 0)

        def LBRACKET(self):
            return self.getToken(BabyDuckParser.LBRACKET, 0)

        def body(self):
            return self.getTypedRuleContext(BabyDuckParser.BodyContext,0)


        def RBRACKET(self):
            return self.getToken(BabyDuckParser.RBRACKET, 0)

        def SEMICOLON(self):
            return self.getToken(BabyDuckParser.SEMICOLON, 0)

        def param_list(self):
            return self.getTypedRuleContext(BabyDuckParser.Param_listContext,0)


        def vars_(self):
            return self.getTypedRuleContext(BabyDuckParser.VarsContext,0)


        def getRuleIndex(self):
            return BabyDuckParser.RULE_funcs

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterFuncs" ):
                listener.enterFuncs(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitFuncs" ):
                listener.exitFuncs(self)




    def funcs(self):

        localctx = BabyDuckParser.FuncsContext(self, self._ctx, self.state)
        self.enterRule(localctx, 8, self.RULE_funcs)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 86
            self.match(BabyDuckParser.VOID)
            self.state = 87
            self.match(BabyDuckParser.ID)
            self.state = 88
            self.match(BabyDuckParser.LPAREN)
            self.state = 90
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==36:
                self.state = 89
                self.param_list()


            self.state = 92
            self.match(BabyDuckParser.RPAREN)
            self.state = 93
            self.match(BabyDuckParser.LBRACKET)
            self.state = 95
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==4:
                self.state = 94
                self.vars_()


            self.state = 97
            self.body()
            self.state = 98
            self.match(BabyDuckParser.RBRACKET)
            self.state = 99
            self.match(BabyDuckParser.SEMICOLON)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Param_listContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self, i:int=None):
            if i is None:
                return self.getTokens(BabyDuckParser.ID)
            else:
                return self.getToken(BabyDuckParser.ID, i)

        def COLON(self, i:int=None):
            if i is None:
                return self.getTokens(BabyDuckParser.COLON)
            else:
                return self.getToken(BabyDuckParser.COLON, i)

        def type_(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(BabyDuckParser.TypeContext)
            else:
                return self.getTypedRuleContext(BabyDuckParser.TypeContext,i)


        def COMMA(self, i:int=None):
            if i is None:
                return self.getTokens(BabyDuckParser.COMMA)
            else:
                return self.getToken(BabyDuckParser.COMMA, i)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_param_list

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterParam_list" ):
                listener.enterParam_list(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitParam_list" ):
                listener.exitParam_list(self)




    def param_list(self):

        localctx = BabyDuckParser.Param_listContext(self, self._ctx, self.state)
        self.enterRule(localctx, 10, self.RULE_param_list)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 101
            self.match(BabyDuckParser.ID)
            self.state = 102
            self.match(BabyDuckParser.COLON)
            self.state = 103
            self.type_()
            self.state = 110
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==30:
                self.state = 104
                self.match(BabyDuckParser.COMMA)
                self.state = 105
                self.match(BabyDuckParser.ID)
                self.state = 106
                self.match(BabyDuckParser.COLON)
                self.state = 107
                self.type_()
                self.state = 112
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class BodyContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def LBRACE(self):
            return self.getToken(BabyDuckParser.LBRACE, 0)

        def RBRACE(self):
            return self.getToken(BabyDuckParser.RBRACE, 0)

        def statement(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(BabyDuckParser.StatementContext)
            else:
                return self.getTypedRuleContext(BabyDuckParser.StatementContext,i)


        def getRuleIndex(self):
            return BabyDuckParser.RULE_body

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterBody" ):
                listener.enterBody(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitBody" ):
                listener.exitBody(self)




    def body(self):

        localctx = BabyDuckParser.BodyContext(self, self._ctx, self.state)
        self.enterRule(localctx, 12, self.RULE_body)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 113
            self.match(BabyDuckParser.LBRACE)
            self.state = 117
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while (((_la) & ~0x3f) == 0 and ((1 << _la) & 68719482112) != 0):
                self.state = 114
                self.statement()
                self.state = 119
                self._errHandler.sync(self)
                _la = self._input.LA(1)

            self.state = 120
            self.match(BabyDuckParser.RBRACE)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class StatementContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def condition(self):
            return self.getTypedRuleContext(BabyDuckParser.ConditionContext,0)


        def cycle(self):
            return self.getTypedRuleContext(BabyDuckParser.CycleContext,0)


        def print_stmt(self):
            return self.getTypedRuleContext(BabyDuckParser.Print_stmtContext,0)


        def assign(self):
            return self.getTypedRuleContext(BabyDuckParser.AssignContext,0)


        def f_call(self):
            return self.getTypedRuleContext(BabyDuckParser.F_callContext,0)


        def getRuleIndex(self):
            return BabyDuckParser.RULE_statement

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterStatement" ):
                listener.enterStatement(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitStatement" ):
                listener.exitStatement(self)




    def statement(self):

        localctx = BabyDuckParser.StatementContext(self, self._ctx, self.state)
        self.enterRule(localctx, 14, self.RULE_statement)
        try:
            self.state = 127
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input,9,self._ctx)
            if la_ == 1:
                self.enterOuterAlt(localctx, 1)
                self.state = 122
                self.condition()
                pass

            elif la_ == 2:
                self.enterOuterAlt(localctx, 2)
                self.state = 123
                self.cycle()
                pass

            elif la_ == 3:
                self.enterOuterAlt(localctx, 3)
                self.state = 124
                self.print_stmt()
                pass

            elif la_ == 4:
                self.enterOuterAlt(localctx, 4)
                self.state = 125
                self.assign()
                pass

            elif la_ == 5:
                self.enterOuterAlt(localctx, 5)
                self.state = 126
                self.f_call()
                pass


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class AssignContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self):
            return self.getToken(BabyDuckParser.ID, 0)

        def EQ(self):
            return self.getToken(BabyDuckParser.EQ, 0)

        def expression(self):
            return self.getTypedRuleContext(BabyDuckParser.ExpressionContext,0)


        def SEMICOLON(self):
            return self.getToken(BabyDuckParser.SEMICOLON, 0)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_assign

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterAssign" ):
                listener.enterAssign(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitAssign" ):
                listener.exitAssign(self)




    def assign(self):

        localctx = BabyDuckParser.AssignContext(self, self._ctx, self.state)
        self.enterRule(localctx, 16, self.RULE_assign)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 129
            self.match(BabyDuckParser.ID)
            self.state = 130
            self.match(BabyDuckParser.EQ)
            self.state = 131
            self.expression()
            self.state = 132
            self.match(BabyDuckParser.SEMICOLON)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class F_callContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self):
            return self.getToken(BabyDuckParser.ID, 0)

        def LPAREN(self):
            return self.getToken(BabyDuckParser.LPAREN, 0)

        def RPAREN(self):
            return self.getToken(BabyDuckParser.RPAREN, 0)

        def SEMICOLON(self):
            return self.getToken(BabyDuckParser.SEMICOLON, 0)

        def expression_list(self):
            return self.getTypedRuleContext(BabyDuckParser.Expression_listContext,0)


        def getRuleIndex(self):
            return BabyDuckParser.RULE_f_call

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterF_call" ):
                listener.enterF_call(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitF_call" ):
                listener.exitF_call(self)




    def f_call(self):

        localctx = BabyDuckParser.F_callContext(self, self._ctx, self.state)
        self.enterRule(localctx, 18, self.RULE_f_call)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 134
            self.match(BabyDuckParser.ID)
            self.state = 135
            self.match(BabyDuckParser.LPAREN)
            self.state = 137
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if (((_la) & ~0x3f) == 0 and ((1 << _la) & 94506082304) != 0):
                self.state = 136
                self.expression_list()


            self.state = 139
            self.match(BabyDuckParser.RPAREN)
            self.state = 140
            self.match(BabyDuckParser.SEMICOLON)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Expression_listContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def expression(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(BabyDuckParser.ExpressionContext)
            else:
                return self.getTypedRuleContext(BabyDuckParser.ExpressionContext,i)


        def COMMA(self, i:int=None):
            if i is None:
                return self.getTokens(BabyDuckParser.COMMA)
            else:
                return self.getToken(BabyDuckParser.COMMA, i)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_expression_list

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterExpression_list" ):
                listener.enterExpression_list(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitExpression_list" ):
                listener.exitExpression_list(self)




    def expression_list(self):

        localctx = BabyDuckParser.Expression_listContext(self, self._ctx, self.state)
        self.enterRule(localctx, 20, self.RULE_expression_list)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 142
            self.expression()
            self.state = 147
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==30:
                self.state = 143
                self.match(BabyDuckParser.COMMA)
                self.state = 144
                self.expression()
                self.state = 149
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ConditionContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def IF(self):
            return self.getToken(BabyDuckParser.IF, 0)

        def LPAREN(self):
            return self.getToken(BabyDuckParser.LPAREN, 0)

        def expression(self):
            return self.getTypedRuleContext(BabyDuckParser.ExpressionContext,0)


        def RPAREN(self):
            return self.getToken(BabyDuckParser.RPAREN, 0)

        def body(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(BabyDuckParser.BodyContext)
            else:
                return self.getTypedRuleContext(BabyDuckParser.BodyContext,i)


        def SEMICOLON(self):
            return self.getToken(BabyDuckParser.SEMICOLON, 0)

        def ELSE(self):
            return self.getToken(BabyDuckParser.ELSE, 0)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_condition

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterCondition" ):
                listener.enterCondition(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitCondition" ):
                listener.exitCondition(self)




    def condition(self):

        localctx = BabyDuckParser.ConditionContext(self, self._ctx, self.state)
        self.enterRule(localctx, 22, self.RULE_condition)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 150
            self.match(BabyDuckParser.IF)
            self.state = 151
            self.match(BabyDuckParser.LPAREN)
            self.state = 152
            self.expression()
            self.state = 153
            self.match(BabyDuckParser.RPAREN)
            self.state = 154
            self.body()
            self.state = 157
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==9:
                self.state = 155
                self.match(BabyDuckParser.ELSE)
                self.state = 156
                self.body()


            self.state = 159
            self.match(BabyDuckParser.SEMICOLON)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class CycleContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def WHILE(self):
            return self.getToken(BabyDuckParser.WHILE, 0)

        def LPAREN(self):
            return self.getToken(BabyDuckParser.LPAREN, 0)

        def expression(self):
            return self.getTypedRuleContext(BabyDuckParser.ExpressionContext,0)


        def RPAREN(self):
            return self.getToken(BabyDuckParser.RPAREN, 0)

        def DO(self):
            return self.getToken(BabyDuckParser.DO, 0)

        def body(self):
            return self.getTypedRuleContext(BabyDuckParser.BodyContext,0)


        def SEMICOLON(self):
            return self.getToken(BabyDuckParser.SEMICOLON, 0)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_cycle

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterCycle" ):
                listener.enterCycle(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitCycle" ):
                listener.exitCycle(self)




    def cycle(self):

        localctx = BabyDuckParser.CycleContext(self, self._ctx, self.state)
        self.enterRule(localctx, 24, self.RULE_cycle)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 161
            self.match(BabyDuckParser.WHILE)
            self.state = 162
            self.match(BabyDuckParser.LPAREN)
            self.state = 163
            self.expression()
            self.state = 164
            self.match(BabyDuckParser.RPAREN)
            self.state = 165
            self.match(BabyDuckParser.DO)
            self.state = 166
            self.body()
            self.state = 167
            self.match(BabyDuckParser.SEMICOLON)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Print_stmtContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def PRINT(self):
            return self.getToken(BabyDuckParser.PRINT, 0)

        def LPAREN(self):
            return self.getToken(BabyDuckParser.LPAREN, 0)

        def print_val(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(BabyDuckParser.Print_valContext)
            else:
                return self.getTypedRuleContext(BabyDuckParser.Print_valContext,i)


        def RPAREN(self):
            return self.getToken(BabyDuckParser.RPAREN, 0)

        def SEMICOLON(self):
            return self.getToken(BabyDuckParser.SEMICOLON, 0)

        def COMMA(self, i:int=None):
            if i is None:
                return self.getTokens(BabyDuckParser.COMMA)
            else:
                return self.getToken(BabyDuckParser.COMMA, i)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_print_stmt

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterPrint_stmt" ):
                listener.enterPrint_stmt(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitPrint_stmt" ):
                listener.exitPrint_stmt(self)




    def print_stmt(self):

        localctx = BabyDuckParser.Print_stmtContext(self, self._ctx, self.state)
        self.enterRule(localctx, 26, self.RULE_print_stmt)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 169
            self.match(BabyDuckParser.PRINT)
            self.state = 170
            self.match(BabyDuckParser.LPAREN)
            self.state = 171
            self.print_val()
            self.state = 176
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==30:
                self.state = 172
                self.match(BabyDuckParser.COMMA)
                self.state = 173
                self.print_val()
                self.state = 178
                self._errHandler.sync(self)
                _la = self._input.LA(1)

            self.state = 179
            self.match(BabyDuckParser.RPAREN)
            self.state = 180
            self.match(BabyDuckParser.SEMICOLON)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Print_valContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def expression(self):
            return self.getTypedRuleContext(BabyDuckParser.ExpressionContext,0)


        def CTE_STRING(self):
            return self.getToken(BabyDuckParser.CTE_STRING, 0)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_print_val

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterPrint_val" ):
                listener.enterPrint_val(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitPrint_val" ):
                listener.exitPrint_val(self)




    def print_val(self):

        localctx = BabyDuckParser.Print_valContext(self, self._ctx, self.state)
        self.enterRule(localctx, 28, self.RULE_print_val)
        try:
            self.state = 184
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [13, 14, 24, 33, 34, 36]:
                self.enterOuterAlt(localctx, 1)
                self.state = 182
                self.expression()
                pass
            elif token in [35]:
                self.enterOuterAlt(localctx, 2)
                self.state = 183
                self.match(BabyDuckParser.CTE_STRING)
                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ExpressionContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def exp(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(BabyDuckParser.ExpContext)
            else:
                return self.getTypedRuleContext(BabyDuckParser.ExpContext,i)


        def rel_op(self):
            return self.getTypedRuleContext(BabyDuckParser.Rel_opContext,0)


        def getRuleIndex(self):
            return BabyDuckParser.RULE_expression

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterExpression" ):
                listener.enterExpression(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitExpression" ):
                listener.exitExpression(self)




    def expression(self):

        localctx = BabyDuckParser.ExpressionContext(self, self._ctx, self.state)
        self.enterRule(localctx, 30, self.RULE_expression)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 186
            self.exp()
            self.state = 190
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if (((_la) & ~0x3f) == 0 and ((1 << _la) & 16515072) != 0):
                self.state = 187
                self.rel_op()
                self.state = 188
                self.exp()


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Rel_opContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def GT(self):
            return self.getToken(BabyDuckParser.GT, 0)

        def LT(self):
            return self.getToken(BabyDuckParser.LT, 0)

        def NEQ(self):
            return self.getToken(BabyDuckParser.NEQ, 0)

        def SAME(self):
            return self.getToken(BabyDuckParser.SAME, 0)

        def AND(self):
            return self.getToken(BabyDuckParser.AND, 0)

        def OR(self):
            return self.getToken(BabyDuckParser.OR, 0)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_rel_op

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterRel_op" ):
                listener.enterRel_op(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitRel_op" ):
                listener.exitRel_op(self)




    def rel_op(self):

        localctx = BabyDuckParser.Rel_opContext(self, self._ctx, self.state)
        self.enterRule(localctx, 32, self.RULE_rel_op)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 192
            _la = self._input.LA(1)
            if not((((_la) & ~0x3f) == 0 and ((1 << _la) & 16515072) != 0)):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ExpContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def term(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(BabyDuckParser.TermContext)
            else:
                return self.getTypedRuleContext(BabyDuckParser.TermContext,i)


        def PLUS(self, i:int=None):
            if i is None:
                return self.getTokens(BabyDuckParser.PLUS)
            else:
                return self.getToken(BabyDuckParser.PLUS, i)

        def MINUS(self, i:int=None):
            if i is None:
                return self.getTokens(BabyDuckParser.MINUS)
            else:
                return self.getToken(BabyDuckParser.MINUS, i)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_exp

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterExp" ):
                listener.enterExp(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitExp" ):
                listener.exitExp(self)




    def exp(self):

        localctx = BabyDuckParser.ExpContext(self, self._ctx, self.state)
        self.enterRule(localctx, 34, self.RULE_exp)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 194
            self.term()
            self.state = 199
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==13 or _la==14:
                self.state = 195
                _la = self._input.LA(1)
                if not(_la==13 or _la==14):
                    self._errHandler.recoverInline(self)
                else:
                    self._errHandler.reportMatch(self)
                    self.consume()
                self.state = 196
                self.term()
                self.state = 201
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class TermContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def factor(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(BabyDuckParser.FactorContext)
            else:
                return self.getTypedRuleContext(BabyDuckParser.FactorContext,i)


        def MULT(self, i:int=None):
            if i is None:
                return self.getTokens(BabyDuckParser.MULT)
            else:
                return self.getToken(BabyDuckParser.MULT, i)

        def DIV(self, i:int=None):
            if i is None:
                return self.getTokens(BabyDuckParser.DIV)
            else:
                return self.getToken(BabyDuckParser.DIV, i)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_term

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterTerm" ):
                listener.enterTerm(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitTerm" ):
                listener.exitTerm(self)




    def term(self):

        localctx = BabyDuckParser.TermContext(self, self._ctx, self.state)
        self.enterRule(localctx, 36, self.RULE_term)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 202
            self.factor()
            self.state = 207
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==15 or _la==16:
                self.state = 203
                _la = self._input.LA(1)
                if not(_la==15 or _la==16):
                    self._errHandler.recoverInline(self)
                else:
                    self._errHandler.reportMatch(self)
                    self.consume()
                self.state = 204
                self.factor()
                self.state = 209
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class FactorContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def LPAREN(self):
            return self.getToken(BabyDuckParser.LPAREN, 0)

        def expression(self):
            return self.getTypedRuleContext(BabyDuckParser.ExpressionContext,0)


        def RPAREN(self):
            return self.getToken(BabyDuckParser.RPAREN, 0)

        def id_cte(self):
            return self.getTypedRuleContext(BabyDuckParser.Id_cteContext,0)


        def plus_minus(self):
            return self.getTypedRuleContext(BabyDuckParser.Plus_minusContext,0)


        def getRuleIndex(self):
            return BabyDuckParser.RULE_factor

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterFactor" ):
                listener.enterFactor(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitFactor" ):
                listener.exitFactor(self)




    def factor(self):

        localctx = BabyDuckParser.FactorContext(self, self._ctx, self.state)
        self.enterRule(localctx, 38, self.RULE_factor)
        self._la = 0 # Token type
        try:
            self.state = 218
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [24]:
                self.enterOuterAlt(localctx, 1)
                self.state = 210
                self.match(BabyDuckParser.LPAREN)
                self.state = 211
                self.expression()
                self.state = 212
                self.match(BabyDuckParser.RPAREN)
                pass
            elif token in [13, 14, 33, 34, 36]:
                self.enterOuterAlt(localctx, 2)
                self.state = 215
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                if _la==13 or _la==14:
                    self.state = 214
                    self.plus_minus()


                self.state = 217
                self.id_cte()
                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Plus_minusContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def PLUS(self):
            return self.getToken(BabyDuckParser.PLUS, 0)

        def MINUS(self):
            return self.getToken(BabyDuckParser.MINUS, 0)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_plus_minus

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterPlus_minus" ):
                listener.enterPlus_minus(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitPlus_minus" ):
                listener.exitPlus_minus(self)




    def plus_minus(self):

        localctx = BabyDuckParser.Plus_minusContext(self, self._ctx, self.state)
        self.enterRule(localctx, 40, self.RULE_plus_minus)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 220
            _la = self._input.LA(1)
            if not(_la==13 or _la==14):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class CteContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def CTE_INT(self):
            return self.getToken(BabyDuckParser.CTE_INT, 0)

        def CTE_FLOAT(self):
            return self.getToken(BabyDuckParser.CTE_FLOAT, 0)

        def getRuleIndex(self):
            return BabyDuckParser.RULE_cte

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterCte" ):
                listener.enterCte(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitCte" ):
                listener.exitCte(self)




    def cte(self):

        localctx = BabyDuckParser.CteContext(self, self._ctx, self.state)
        self.enterRule(localctx, 42, self.RULE_cte)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 222
            _la = self._input.LA(1)
            if not(_la==33 or _la==34):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Id_cteContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ID(self):
            return self.getToken(BabyDuckParser.ID, 0)

        def cte(self):
            return self.getTypedRuleContext(BabyDuckParser.CteContext,0)


        def getRuleIndex(self):
            return BabyDuckParser.RULE_id_cte

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterId_cte" ):
                listener.enterId_cte(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitId_cte" ):
                listener.exitId_cte(self)




    def id_cte(self):

        localctx = BabyDuckParser.Id_cteContext(self, self._ctx, self.state)
        self.enterRule(localctx, 44, self.RULE_id_cte)
        try:
            self.state = 226
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [36]:
                self.enterOuterAlt(localctx, 1)
                self.state = 224
                self.match(BabyDuckParser.ID)
                pass
            elif token in [33, 34]:
                self.enterOuterAlt(localctx, 2)
                self.state = 225
                self.cte()
                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx





