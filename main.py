import sys
import os
import json
import argparse
from antlr4 import *
from generated.BabyDuckLexer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from generated.BabyDuckParser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from semantic_listener import <PERSON><PERSON>uckSemanticListener
from debug_utils import SemanticTablePrinter
from code_generator import CodeGenerator
from virtual_machine import VirtualMachine

def load_constants_from_file(constants_file):
    """Load constants from .const file generated by compiler."""
    try:
        with open(constants_file, 'r') as f:
            constant_table = json.load(f)

        # Convert string keys back to appropriate types and values
        constants = {}
        for value_str, address in constant_table.items():
            # Try to convert value back to appropriate type
            try:
                # Try integer first
                value = int(value_str)
            except ValueError:
                try:
                    # Try float
                    value = float(value_str)
                except ValueError:
                    # Keep as string
                    value = value_str

            constants[address] = value

        return constants
    except Exception as e:
        print(f"Warning: Could not load constants from {constants_file}: {e}")
        return {}

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='BabyDuck Compiler')
    parser.add_argument('input_file', nargs='?', default='test.bduck',
                       help='BabyDuck source file to compile')
    parser.add_argument('--run', action='store_true',
                       help='Compile and execute the program in one step')
    parser.add_argument('--debug', action='store_true',
                       help='Show compilation details (when used with --run)')

    args = parser.parse_args()
    input_file = args.input_file

    # Revisamos si el archivo existe
    if not os.path.exists(input_file):
        print(f"Error: File '{input_file}' not found.")
        return

    # Only show compilation message if not running or if debug is enabled
    if not args.run or args.debug:
        print(f"Compiling {input_file}...")

    # Leemos el archivo de entrada
    with open(input_file, "r") as file:
        input_text = file.read()
        input_stream = InputStream(input_text)

    # Creamos el lexer y el parser
    lexer = BabyDuckLexer(input_stream)
    stream = CommonTokenStream(lexer)

    parser = BabyDuckParser(stream)

    # parseamos el programa
    try:
        tree = parser.program()

        # Revisamos para errores de sintaxis
        if parser.getNumberOfSyntaxErrors() > 0:
            print(f"\n{parser.getNumberOfSyntaxErrors()} syntax errors found.")
            return

        # Imprimimos el arbol de parseo solo si debug esta habilitado
        if not args.run or args.debug:
            print("\n===== PARSE TREE =====")
            print(tree.toStringTree(recog=parser))

        # Creamos y corremos el analizador semantico
        if not args.run or args.debug:
            print("\n===== SEMANTIC ANALYSIS =====")

        # Create listener with quiet mode if running without debug
        quiet_mode = args.run and not args.debug
        listener = BabyDuckSemanticListener(quiet=quiet_mode)
        walker = ParseTreeWalker()
        walker.walk(listener, tree)

        # Imprimimos los resultados del analisis semantico
        if listener.analyzer.errors:
            print(f"\n{len(listener.analyzer.errors)} semantic errors found.")
            # Los errores ya fueron impresos por el analizador
        else:
            if not args.run or args.debug:
                print("\nNo semantic errors found. Program is semantically correct.")

            # La generacion de codigo se manega en el listener
            if not args.run or args.debug:
                print("\n===== CODE GENERATION =====")
                print("Quadruples have been generated successfully.")

            # Guardamos los cuadruplos a un archivo
            output_file = os.path.splitext(input_file)[0] + ".quad"
            with open(output_file, "w") as f:
                f.write("===== QUADRUPLES =====\n")
                for i, quad in enumerate(listener.code_generator.quadruples):
                    f.write(f"{i}: {quad}\n")

            # Guardamos la tabla de constantes a un archivo
            constants_file = os.path.splitext(input_file)[0] + ".const"
            constant_table = listener.code_generator.get_constant_table()
            with open(constants_file, "w") as f:
                json.dump(constant_table, f, indent=2)

            if not args.run or args.debug:
                print(f"Quadruples saved to {output_file}")
                print(f"Constants saved to {constants_file}")

            # Execute the program if --run flag is provided
            if args.run:
                if not args.debug:
                    print()  # Add a blank line before execution output

                # Create virtual machine
                vm = VirtualMachine(debug=args.debug)

                # Load constants
                constants = load_constants_from_file(constants_file)

                # Load and execute the program
                if vm.load_program(output_file, constants):
                    # Load constants into memory
                    for address, value in constants.items():
                        vm.memory.set_value(address, value)

                    try:
                        vm.execute()
                    except KeyboardInterrupt:
                        print("\nProgram execution interrupted by user")
                    except Exception as e:
                        print(f"Runtime error: {e}")
                else:
                    print("Failed to load program for execution")

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()