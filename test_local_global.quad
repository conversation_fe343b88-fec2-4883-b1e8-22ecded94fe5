===== QUADRUPLES =====
0: (QuadrupleType.GOTO, None, None, 10)
1: (QuadrupleType.PLUS, 5000, 5001, 9000)
2: (QuadrupleType.ASSIGN, 9000, None, 5002)
3: (QuadrupleType.PRINT, None, None, 5000)
4: (QuadrupleType.NEWLINE, None, None, None)
5: (QuadrupleType.PRINT, None, None, 5001)
6: (QuadrupleType.NEWLINE, None, None, None)
7: (QuadrupleType.PRINT, None, None, 5002)
8: (QuadrupleType.NEWLINE, None, None, None)
9: (QuadrupleType.ENDPROC, None, None, None)
10: (QuadrupleType.ASSIGN, 13000, None, 1000)
11: (QuadrupleType.ASSIGN, 13001, None, 1001)
12: (QuadrupleType.ASSIGN, 14000, None, 2000)
13: (QuadrupleType.PRINT, None, None, 2000)
14: (QuadrupleType.NEW<PERSON><PERSON><PERSON>, None, None, None)
15: (QuadrupleType.PRIN<PERSON>, None, None, 15000)
16: (QuadrupleType.<PERSON><PERSON><PERSON><PERSON><PERSON>, None, None, None)
17: (QuadrupleType.ERA, None, None, funny)
18: (QuadrupleType.PARAM, 13002, None, 0)
19: (QuadrupleType.PARAM, 13003, None, 1)
20: (QuadrupleType.GOSUB, None, None, 1)
21: (QuadrupleType.PRINT, None, None, 15001)
22: (QuadrupleType.NEWLINE, None, None, None)
23: (QuadrupleType.PRINT, None, None, 1000)
24: (QuadrupleType.NEWLINE, None, None, None)
25: (QuadrupleType.PRINT, None, None, 1001)
26: (QuadrupleType.NEWLINE, None, None, None)
27: (QuadrupleType.END, None, None, None)
