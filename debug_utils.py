"""
Debugging utilities for BabyDuck compiler
Provides visualization of semantic tables
"""
import json
from semantic_analyzer import Type, VarTable, FunctionDirectory

def type_to_str(type_val):
    """Convert Type enum to string representation."""
    if type_val == Type.INT:
        return "INT"
    elif type_val == Type.FLOAT:
        return "FLOAT"
    elif type_val == Type.VOID:
        return "VOID"
    elif type_val == Type.STRING:
        return "STRING"
    elif type_val == Type.ERROR:
        return "ERROR"
    return "UNKNOWN"

class SemanticTablePrinter:
    """Pretty printer for semantic tables."""

    @staticmethod
    def print_function_directory(func_dir):
        """Print function directory in a formatted way."""
        print("\n===== FUNCTION DIRECTORY =====")
        print(f"{'FUNCTION':<15} {'RETURN TYPE':<12} {'PARAMETERS':<30} {'VARIABLES':<20}")
        print("-" * 80)

        # Print global scope first
        print(f"{'GLOBAL':<15} {'N/A':<12}", end=" ")
        print(f"{'-':<30}", end=" ")
        var_str = ", ".join([f"{name}:{type_to_str(var.type)}" for name, var in func_dir.global_var_table.vars.items()])
        print(f"{var_str:<20}")

        # Print each function
        for name, func in func_dir.functions.items():
            return_type_str = type_to_str(func.return_type)
            params_str = ", ".join([f"{p.name}:{type_to_str(p.type)}" for p in func.params])
            if not params_str:
                params_str = "-"
            vars_str = ", ".join([f"{name}:{type_to_str(var.type)}" for name, var in func.var_table.vars.items()])
            if not vars_str:
                vars_str = "-"
            print(f"{name:<15} {return_type_str:<12} {params_str:<30} {vars_str:<20}")

    @staticmethod
    def print_semantic_cube(cube):
        """Print semantic cube in a formatted way."""
        print("\n===== SEMANTIC CUBE (Sample) =====")
        # Just print a few sample entries for illustration
        print("INT + INT = INT")
        print("INT + FLOAT = FLOAT")
        print("FLOAT * INT = FLOAT")
        print("INT / INT = FLOAT")
        print("INT < FLOAT = INT (boolean)")

    @staticmethod
    def generate_json_tables(func_dir):
        """Generate JSON representation of tables for visualization."""
        result = {
            "global_vars": {},
            "functions": {}
        }

        # Add global variables
        for name, var in func_dir.global_var_table.vars.items():
            result["global_vars"][name] = {
                "type": type_to_str(var.type)
            }

        # Add functions
        for name, func in func_dir.functions.items():
            func_entry = {
                "return_type": type_to_str(func.return_type),
                "params": [],
                "vars": {}
            }

            # Add parameters
            for param in func.params:
                func_entry["params"].append({
                    "name": param.name,
                    "type": type_to_str(param.type)
                })

            # Add local variables
            for var_name, var in func.var_table.vars.items():
                func_entry["vars"][var_name] = {
                    "type": type_to_str(var.type)
                }

            result["functions"][name] = func_entry

        return json.dumps(result, indent=2)

    @staticmethod
    def save_tables_to_file(func_dir, filename="semantic_tables.json", quiet=False):
        """Save tables to a JSON file for visualization."""
        json_data = SemanticTablePrinter.generate_json_tables(func_dir)
        with open(filename, "w") as f:
            f.write(json_data)
        if not quiet:
            print(f"\nSemantic tables saved to {filename}")