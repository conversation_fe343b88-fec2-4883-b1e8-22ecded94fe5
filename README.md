# BabyDuck Compiler and Virtual Machine

Compilador y máquina virtual para el lenguaje de programación BabyDuck.

## Prerequisites

- Python 3.7 al menos - Java Runtime Environment (JRE) -requerido para la generación de gramática ANTLR4

3. Download ANTLR4 from [https://www.antlr.org/download.html](https://www.antlr.org/download.html) or use the jar file included in the repository

## Uso

### Genera parser y lexer desde la gramática

java -jar C:\JavaLib\antlr-4.13.2-complete.jar -Dlanguage=Python3 -o ../generated BabyDuck.g4

Note: Adjust the path to the ANTLR jar file according to your system.

### Compilado y ejecución de programas BabyDuck

```python main.py <filename>.bduck --run

```

Ejemplos programs:

````python main.py test_simple.bduck --run
python main.py fibonacci_test.bduck --run
python main.py function_calls.bduck --run
python main.py test_local_global.bduck --run```
Para ver detalles del compilado:
````

python main.py <filename>.bduck --run --debug```
To check for semantic errors:

````
python main.py test_simple_error.bduck --run```
### Running Compiled Programs
To run a previously compiled program:
````

python run_program.py <filename>.quad```
With debug information:

````
python run_program.py <filename>.quad --debug```
## Running Tests
To run the test suite:
````

python test_runner.py

```








































```
