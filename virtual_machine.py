"""
BabyDuck Virtual Machine
-----------------------
This module implements a virtual machine to execute the quadruples generated
by the BabyDuck compiler. It includes execution memory management and
interpretation of all operation codes.
"""

from code_generator import QuadrupleType, Type
import json

class ExecutionMemory:
    """Memory management for the virtual machine."""

    def __init__(self):
        # Main memory storage: address -> value
        self.memory = {}

        # Memory segment boundaries
        self.GLOBAL_START = 1000
        self.GLOBAL_END = 4999
        self.LOCAL_START = 5000
        self.LOCAL_END = 8999
        self.TEMP_START = 9000
        self.TEMP_END = 12999
        self.CONST_START = 13000
        self.CONST_END = 16999

        # Type boundaries within each segment
        self.TYPE_SEGMENT_SIZE = 1000

        # Add stacks for function calls
        self.return_stack = []  # Stack for return addresses
        self.call_stack = []    # Stack for function call frames

        # Current instruction pointer
        self.instruction_pointer = 0

        # Program execution state
        self.running = True

    def get_memory_segment(self, address):
        """Determine which memory segment an address belongs to."""
        if self.GLOBAL_START <= address <= self.GLOBAL_END:
            return "GLOBAL"
        elif self.LOCAL_START <= address <= self.LOCAL_END:
            return "LOCAL"
        elif self.TEMP_START <= address <= self.TEMP_END:
            return "TEMPORARY"
        elif self.CONST_START <= address <= self.CONST_END:
            return "CONSTANT"
        else:
            return "INVALID"

    def get_value_type(self, address):
        """Determine the data type based on address."""
        segment_start = None
        if self.GLOBAL_START <= address <= self.GLOBAL_END:
            segment_start = self.GLOBAL_START
        elif self.LOCAL_START <= address <= self.LOCAL_END:
            segment_start = self.LOCAL_START
        elif self.TEMP_START <= address <= self.TEMP_END:
            segment_start = self.TEMP_START
        elif self.CONST_START <= address <= self.CONST_END:
            segment_start = self.CONST_START

        if segment_start is None:
            return Type.ERROR

        offset = address - segment_start
        if 0 <= offset < self.TYPE_SEGMENT_SIZE:
            return Type.INT
        elif self.TYPE_SEGMENT_SIZE <= offset < self.TYPE_SEGMENT_SIZE * 2:
            return Type.FLOAT
        elif self.TYPE_SEGMENT_SIZE * 2 <= offset < self.TYPE_SEGMENT_SIZE * 3:
            return Type.STRING
        else:
            return Type.VOID  # Used for boolean results

    def get_value(self, address):
        """Get value from memory address."""
        if address is None:
            return None
        if address not in self.memory:
            # Initialize with default value based on type
            value_type = self.get_value_type(address)
            if value_type == Type.INT or value_type == Type.VOID:
                self.memory[address] = 0
            elif value_type == Type.FLOAT:
                self.memory[address] = 0.0
            elif value_type == Type.STRING:
                self.memory[address] = ""
            else:
                self.memory[address] = None
        return self.memory[address]

    def set_value(self, address, value):
        """Set value at memory address."""
        if address is None:
            return
        self.memory[address] = value

    def clear_local_memory(self):
        """Clear local memory segment (for function calls)."""
        keys_to_remove = [addr for addr in self.memory.keys()
                         if self.LOCAL_START <= addr <= self.LOCAL_END]
        for key in keys_to_remove:
            del self.memory[key]

    def clear_temp_memory(self):
        """Clear temporary memory segment."""
        keys_to_remove = [addr for addr in self.memory.keys()
                         if self.TEMP_START <= addr <= self.TEMP_END]
        for key in keys_to_remove:
            del self.memory[key]

    def push_call_frame(self):
        """Save current local memory state for function call."""
        local_memory = {addr: value for addr, value in self.memory.items()
                       if self.LOCAL_START <= addr <= self.LOCAL_END}
        self.call_stack.append(local_memory)
        self.clear_local_memory()

    def pop_call_frame(self):
        """Restore previous local memory state after function return."""
        if self.call_stack:
            local_memory = self.call_stack.pop()
            # Clear current local memory
            self.clear_local_memory()
            # Restore previous local memory
            self.memory.update(local_memory)

    def print_memory_state(self):
        """Print current memory state for debugging."""
        print("\n===== MEMORY STATE =====")

        segments = {
            "GLOBAL": (self.GLOBAL_START, self.GLOBAL_END),
            "LOCAL": (self.LOCAL_START, self.LOCAL_END),
            "TEMPORARY": (self.TEMP_START, self.TEMP_END),
            "CONSTANT": (self.CONST_START, self.CONST_END)
        }

        for segment_name, (start, end) in segments.items():
            segment_memory = {addr: value for addr, value in self.memory.items()
                            if start <= addr <= end}
            if segment_memory:
                print(f"\n{segment_name} MEMORY:")
                for addr in sorted(segment_memory.keys()):
                    value = segment_memory[addr]
                    value_type = self.get_value_type(addr)
                    print(f"  {addr}: {value} ({value_type})")

    def prepare_function_call(self, func_name):
        """Prepare for a function call by creating a new call frame."""
        # Create a new call frame (could be a dictionary to store local variables)
        new_frame = {}
        self.call_stack.append(new_frame)



    def push_return_address(self, address):
        """Push a return address onto the return stack."""
        self.return_stack.append(address)

    def pop_return_address(self):
        """Pop a return address from the return stack."""
        if self.return_stack:
            return self.return_stack.pop()
        return 0  # Default return address


class VirtualMachine:
    """
    BabyDuck Virtual Machine

    Executes quadruples generated by the BabyDuck compiler.
    Implements all operation codes defined in QuadrupleType.
    """

    def __init__(self, debug=False):
        self.memory = ExecutionMemory()
        self.quadruples = []
        self.constants = {}  # Maps address -> constant value
        self.debug = debug  # Debug mode flag
        self.current_function = None  # Track the current function being called
        self.function_addresses = {}  # Maps function name -> start address
        self.function_variable_layouts = {}  # Maps function name -> variable layout info

    def set_parameter(self, param_index, value):
        """Set a parameter value in the current call frame."""
        # Use the current function name to determine the correct parameter address
        # This is needed because different functions have different memory layouts

        if self.current_function and self.current_function in self.function_variable_layouts:
            # Get the parameter layout for the current function
            layout = self.function_variable_layouts[self.current_function]
            if 'parameters' in layout and param_index < len(layout['parameters']):
                param_address = layout['parameters'][param_index]['address']
            else:
                # Fallback to default mapping if layout not found
                param_address = 5000 + param_index
        else:
            # Fallback if no function context or layout
            param_address = 5000 + param_index

        self.memory.set_value(param_address, value)
        if hasattr(self, 'debug') and self.debug:
            print(f"    -> Parameter {param_index} set to address {param_address} with value {value}")

    def load_program(self, quadruples_file, constants=None, function_info=None):
        """Load quadruples from file."""
        try:
            with open(quadruples_file, 'r') as f:
                content = f.read()

            # Parse quadruples from the file
            lines = content.strip().split('\n')
            self.quadruples = []

            for line in lines:
                if line.startswith('===') or not line.strip():
                    continue

                # Parse line format: "0: (QuadrupleType.ASSIGN, 13000, None, 1000)"
                if ':' in line:
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        quad_str = parts[1].strip()
                        # Remove parentheses and split by comma
                        quad_str = quad_str.strip('()')
                        quad_parts = [part.strip() for part in quad_str.split(',')]

                        if len(quad_parts) >= 4:
                            op = quad_parts[0].replace('QuadrupleType.', '')

                            # Helper function to parse operand (could be int or string)
                            def parse_operand(operand_str):
                                if operand_str == 'None':
                                    return None
                                try:
                                    return int(operand_str)
                                except ValueError:
                                    # If it's not an integer, keep it as a string
                                    return operand_str

                            operand1 = parse_operand(quad_parts[1])
                            operand2 = parse_operand(quad_parts[2])
                            result = parse_operand(quad_parts[3])

                            self.quadruples.append((op, operand1, operand2, result))

            # Load constants if provided
            if constants:
                self.constants = constants

            # Load function information if provided
            if function_info:
                self.function_addresses = function_info.get('addresses', {})
                self.function_variable_layouts = function_info.get('layouts', {})

            # Analyze quadruples to find function start addresses if not provided
            if not self.function_addresses:
                self._analyze_function_addresses()

            if self.debug:
                print(f"Loaded {len(self.quadruples)} quadruples")
                print(f"Function addresses: {self.function_addresses}")
            return True

        except Exception as e:
            print(f"Error loading program: {e}")
            return False

    def _analyze_function_addresses(self):
        """Analyze quadruples to determine function start addresses."""
        # Find function start addresses by looking for ERA operations followed by GOSUB
        current_era_function = None

        for i, quad in enumerate(self.quadruples):
            op, _, _, result = quad

            if op == "ERA" and result:
                # Store the function name from ERA
                current_era_function = result
            elif op == "GOSUB" and current_era_function:
                # The GOSUB result should point to the function start
                if result and isinstance(result, int):
                    self.function_addresses[current_era_function] = result
                current_era_function = None

        # If we still have functions without addresses, try to find them by scanning for ENDPROC
        # Functions typically start after the initial GOTO and before their ENDPROC
        if not self.function_addresses:
            # Look for the pattern: functions start after the initial GOTO
            main_start = None
            for i, quad in enumerate(self.quadruples):
                op, _, _, result = quad
                if op == "GOTO" and i == 0:  # Initial GOTO
                    main_start = result
                    break

            # Functions start at address 1 and go until main starts
            if main_start:
                # For now, assume functions start at address 1
                # This is a fallback - ideally addresses should come from compilation
                function_names = set()
                for quad in self.quadruples:
                    if quad[0] == "ERA" and quad[3]:
                        function_names.add(quad[3])

                # Assign sequential addresses starting from 1
                for i, func_name in enumerate(sorted(function_names)):
                    self.function_addresses[func_name] = 1  # All point to 1 for now - needs proper implementation

    def execute(self):
        """Execute the loaded program."""
        if not self.quadruples:
            print("No program loaded")
            return

        if self.debug:
            print("\n===== PROGRAM EXECUTION =====")
        self.memory.instruction_pointer = 0
        self.memory.running = True

        while self.memory.running and self.memory.instruction_pointer < len(self.quadruples):
            quad = self.quadruples[self.memory.instruction_pointer]
            self.execute_quadruple(quad)

            if self.memory.running:
                self.memory.instruction_pointer += 1

        if self.debug:
            print("\n===== EXECUTION COMPLETED =====")

    def execute_quadruple(self, quadruple):
        """Execute a single quadruple."""
        op, operand1, operand2, result = quadruple

        # Debug output
        if self.debug:
            print(f"Executing {self.memory.instruction_pointer}: {op} {operand1} {operand2} {result}")
            # Special debug for LESS_THAN operations
            if op == "LESS_THAN":
                val1 = self.memory.get_value(operand1)
                val2 = self.memory.get_value(operand2)
                print(f"  LESS_THAN: {val1} < {val2} = {val1 < val2}")

        # Arithmetic Operations
        if op == "PLUS":
            val1 = self.memory.get_value(operand1)
            val2 = self.memory.get_value(operand2)
            result_val = val1 + val2
            self.memory.set_value(result, result_val)
            if self.debug:
                print(f"  PLUS: {val1} + {val2} = {result_val} -> {result}")

        elif op == "MINUS":
            val1 = self.memory.get_value(operand1)
            val2 = self.memory.get_value(operand2)
            self.memory.set_value(result, val1 - val2)

        elif op == "MULT":
            val1 = self.memory.get_value(operand1)
            val2 = self.memory.get_value(operand2)
            self.memory.set_value(result, val1 * val2)

        elif op == "DIV":
            val1 = self.memory.get_value(operand1)
            val2 = self.memory.get_value(operand2)
            if val2 == 0:
                print("Runtime Error: Division by zero")
                self.memory.running = False
                return
            self.memory.set_value(result, val1 / val2)

        # Relational Operations
        elif op == "GREATER_THAN":
            val1 = self.memory.get_value(operand1)
            val2 = self.memory.get_value(operand2)
            self.memory.set_value(result, 1 if val1 > val2 else 0)

        elif op == "LESS_THAN":
            val1 = self.memory.get_value(operand1)
            val2 = self.memory.get_value(operand2)
            self.memory.set_value(result, 1 if val1 < val2 else 0)

        elif op == "GREATER_EQUAL":
            val1 = self.memory.get_value(operand1)
            val2 = self.memory.get_value(operand2)
            self.memory.set_value(result, 1 if val1 >= val2 else 0)

        elif op == "LESS_EQUAL":
            val1 = self.memory.get_value(operand1)
            val2 = self.memory.get_value(operand2)
            self.memory.set_value(result, 1 if val1 <= val2 else 0)

        elif op == "EQUAL":
            val1 = self.memory.get_value(operand1)
            val2 = self.memory.get_value(operand2)
            self.memory.set_value(result, 1 if val1 == val2 else 0)

        elif op == "NOT_EQUAL":
            val1 = self.memory.get_value(operand1)
            val2 = self.memory.get_value(operand2)
            self.memory.set_value(result, 1 if val1 != val2 else 0)

        elif op == "AND":
            val1 = self.memory.get_value(operand1)
            val2 = self.memory.get_value(operand2)
            self.memory.set_value(result, 1 if val1 and val2 else 0)

        elif op == "OR":
            val1 = self.memory.get_value(operand1)
            val2 = self.memory.get_value(operand2)

        # Assignment
        elif op == "ASSIGN":
            val = self.memory.get_value(operand1)
            self.memory.set_value(result, val)

        # Control Flow
        elif op == "GOTO":
            self.memory.instruction_pointer = result - 1  # -1 because it will be incremented

        elif op == "GOTOF":
            condition = self.memory.get_value(operand1)
            if self.debug:
                print(f"  GOTOF: condition = {condition}, jumping = {not condition or condition == 0}")
            if not condition or condition == 0:
                self.memory.instruction_pointer = result - 1  # -1 because it will be incremented

        # I/O Operations
        elif op == "PRINT":
            if result is not None:
                value = self.memory.get_value(result)
                print(value, end='', flush=True)

        elif op == "NEWLINE":
            print()  # Just print a newline

        # Function Operations
        elif op == "CALL":
            # Save current instruction pointer and push call frame
            self.memory.push_call_frame()
            self.memory.instruction_pointer = result - 1  # Jump to function
            return  # Return to skip the normal increment

        elif op == "RETURN":
            # Restore previous call frame
            self.memory.pop_call_frame()
            # Return value handling would go here if functions returned values

        elif op == "ENDPROC":
            # End of function - return to caller
            self.memory.pop_call_frame()
            # Restore the return address
            return_address = self.memory.pop_return_address()
            if return_address:
                self.memory.instruction_pointer = return_address - 1  # -1 because it will be incremented
            return  # Return to skip the normal increment

        # Program Control
        elif op == "END":
            self.memory.running = False

        # Function call operations
        elif op == "ERA":
            # Prepare for function call (allocate memory for function)
            self.current_function = result  # Store the function name
            self.memory.prepare_function_call(result)  # result contains function name

        elif op == "PARAM":
            # Set parameter value
            # operand1 contains the value, result contains the parameter index
            param_index = result
            value = self.memory.get_value(operand1)
            if self.debug:
                print(f"  PARAM: Setting parameter {param_index} = {value} to function {self.current_function}")
            self.set_parameter(param_index, value)

        elif op == "GOSUB":
            # Save current instruction pointer
            return_address = self.memory.instruction_pointer + 1
            self.memory.push_return_address(return_address)

            # Get the actual function address
            if self.current_function and self.current_function in self.function_addresses:
                function_address = self.function_addresses[self.current_function]
            else:
                # Fallback to the result value if function address not found
                function_address = result

            # Jump to function start
            self.memory.instruction_pointer = function_address - 1  # -1 because it will be incremented after
            return  # Return to skip the normal increment


        else:
            print(f"Unknown operation: {op}")
            self.memory.running = False


