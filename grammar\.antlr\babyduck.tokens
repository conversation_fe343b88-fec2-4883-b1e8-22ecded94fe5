PROGRAM=1
MAIN=2
END=3
VAR=4
INT=5
FLOAT=6
VOID=7
IF=8
ELSE=9
WHILE=10
DO=11
PRINT=12
PLUS=13
MINUS=14
MULT=15
DIV=16
EQ=17
AND=18
OR=19
SAME=20
NEQ=21
LT=22
GT=23
LPAREN=24
RPAREN=25
LBRACE=26
RBRACE=27
COLON=28
SEMICOLON=29
COMMA=30
LBRACKET=31
RBRACKET=32
CTE_INT=33
CTE_FLOAT=34
CTE_STRING=35
ID=36
WS=37
LINE_COMMENT=38
'program'=1
'main'=2
'end'=3
'var'=4
'int'=5
'float'=6
'void'=7
'if'=8
'else'=9
'while'=10
'do'=11
'print'=12
'+'=13
'-'=14
'*'=15
'/'=16
'='=17
'&&'=18
'||'=19
'=='=20
'!='=21
'<'=22
'>'=23
'('=24
')'=25
'{'=26
'}'=27
':'=28
';'=29
','=30
'['=31
']'=32
