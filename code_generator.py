"""
BabyDuck Code Generator
----------------------
<PERSON>ste módulo implementa la generación de código intermedio (cuádruplos) para el compilador BabyDuck.
Incluye:
1. Pilas para operadores, operandos y tipos
2. Fila de cuádruplos
3. Algoritmos de traducción para expresiones y estatutos
"""

from collections import deque
from enum import Enum
from semantic_analyzer import Type, Operator

class QuadrupleType(Enum):
    """Tipos de operaciones en los cuádruplos."""
    PLUS = '+'
    MINUS = '-'
    MULT = '*'
    DIV = '/'
    ASSIGN = '='
    GREATER_THAN = '>'
    LESS_THAN = '<'
    GREATER_EQUAL = '>='
    LESS_EQUAL = '<='
    EQUAL = '=='
    AND = '&&'
    OR = '||'
    NOT_EQUAL = '!='
    GOTO = 'GOTO'
    GOTOF = 'GOTOF'
    GOTOV = 'GOTOV'
    PRINT = 'PRINT'
    NEWLINE = 'NEWLINE'  # Para saltos de línea explícitos
    ERA = 'ERA'     # Expansion of Runtime Area (para llamadas a funciones)
    PARAM = 'PARAM'
    GOSUB = 'GOSUB'
    RETURN = 'RETURN'
    ENDPROC = 'ENDPROC'
    END = 'END'

class MemoryManager:
    """
    Administrador de memoria para el compilador.

    Maneja la asignación de direcciones virtuales para variables, constantes y temporales.
    La distribución de memoria es la siguiente:

    GLOBALES (1000-4999):
        INT:    1000-1999
        FLOAT:  2000-2999
        STRING: 3000-3999
        BOOL:   4000-4999

    LOCALES (5000-8999):
        INT:    5000-5999
        FLOAT:  6000-6999
        STRING: 7000-7999
        BOOL:   8000-8999

    TEMPORALES (9000-12999):
        INT:    9000-9999
        FLOAT:  10000-10999
        STRING: 11000-11999
        BOOL:   12000-12999

    CONSTANTES (13000-16999):
        INT:    13000-13999
        FLOAT:  14000-14999
        STRING: 15000-15999
        BOOL:   16000-16999
    """

    def __init__(self):
        # Direcciones base para cada segmento de memoria
        self.global_base = 1000
        self.local_base = 5000
        self.temp_base = 9000
        self.const_base = 13000

        # Tamaño de cada segmento de tipo
        self.type_segment_size = 1000

        # Contadores para cada tipo en cada segmento
        self.global_counters = {
            Type.INT: 0,
            Type.FLOAT: 0,
            Type.STRING: 0,
            Type.VOID: 0  # No se usa, pero se incluye para completitud
        }

        self.local_counters = {
            Type.INT: 0,
            Type.FLOAT: 0,
            Type.STRING: 0,
            Type.VOID: 0
        }

        self.temp_counters = {
            Type.INT: 0,
            Type.FLOAT: 0,
            Type.STRING: 0,
            Type.VOID: 0
        }

        self.const_counters = {
            Type.INT: 0,
            Type.FLOAT: 0,
            Type.STRING: 0,
            Type.VOID: 0
        }

        # Tabla de constantes (valor -> dirección)
        self.constant_table = {}

        # Mapeo de tipos a offsets dentro de cada segmento
        self.type_offsets = {
            Type.INT: 0,
            Type.FLOAT: self.type_segment_size,
            Type.STRING: self.type_segment_size * 2,
            Type.VOID: self.type_segment_size * 3  # Para booleanos y otros tipos especiales
        }

    def get_global_address(self, type_val):
        """
        Obtiene una dirección para una variable global.

        Args:
            type_val (Type): El tipo de la variable

        Returns:
            int: La dirección virtual asignada

        Raises:
            ValueError: Si se excede el límite de variables de un tipo
        """
        if self.global_counters[type_val] >= self.type_segment_size:
            raise ValueError(f"Límite de variables globales de tipo {type_val} excedido")

        # Calcular la dirección usando el offset del tipo
        type_offset = self.type_offsets[type_val]
        address = self.global_base + type_offset + self.global_counters[type_val]

        # Incrementar el contador para este tipo
        self.global_counters[type_val] += 1

        return address

    def get_local_address(self, type_val):
        """
        Obtiene una dirección para una variable local.

        Args:
            type_val (Type): El tipo de la variable

        Returns:
            int: La dirección virtual asignada

        Raises:
            ValueError: Si se excede el límite de variables de un tipo
        """
        if self.local_counters[type_val] >= self.type_segment_size:
            raise ValueError(f"Límite de variables locales de tipo {type_val} excedido")

        # Calcular la dirección usando el offset del tipo
        type_offset = self.type_offsets[type_val]
        address = self.local_base + type_offset + self.local_counters[type_val]

        # Incrementar el contador para este tipo
        self.local_counters[type_val] += 1

        return address

    def get_temp_address(self, type_val):
        """
        Obtiene una dirección para una variable temporal.

        Args:
            type_val (Type): El tipo de la variable

        Returns:
            int: La dirección virtual asignada

        Raises:
            ValueError: Si se excede el límite de variables de un tipo
        """
        if self.temp_counters[type_val] >= self.type_segment_size:
            raise ValueError(f"Límite de variables temporales de tipo {type_val} excedido")

        # Calcular la dirección usando el offset del tipo
        type_offset = self.type_offsets[type_val]
        address = self.temp_base + type_offset + self.temp_counters[type_val]

        # Incrementar el contador para este tipo
        self.temp_counters[type_val] += 1

        return address

    def get_constant_address(self, value, type_val):
        """
        Obtiene una dirección para una constante.

        Args:
            value: El valor de la constante
            type_val (Type): El tipo de la constante

        Returns:
            int: La dirección virtual asignada

        Raises:
            ValueError: Si se excede el límite de constantes de un tipo
        """
        # Verificar si la constante ya existe
        if value in self.constant_table:
            return self.constant_table[value]

        if self.const_counters[type_val] >= self.type_segment_size:
            raise ValueError(f"Límite de constantes de tipo {type_val} excedido")

        # Calcular la dirección usando el offset del tipo
        type_offset = self.type_offsets[type_val]
        address = self.const_base + type_offset + self.const_counters[type_val]

        # Guardar la constante en la tabla y actualizar el contador
        self.constant_table[value] = address
        self.const_counters[type_val] += 1

        return address

    def reset_local_counters(self):
        """
        Resetea los contadores locales (al cambiar de función).
        Esto permite reutilizar las direcciones locales para cada función.
        """
        self.local_counters = {
            Type.INT: 0,
            Type.FLOAT: 0,
            Type.STRING: 0,
            Type.VOID: 0
        }

    def reset_temp_counters(self):
        """
        Resetea los contadores temporales (al cambiar de función).
        Esto permite reutilizar las direcciones temporales para cada función.
        """
        self.temp_counters = {
            Type.INT: 0,
            Type.FLOAT: 0,
            Type.STRING: 0,
            Type.VOID: 0
        }

    def get_address_type(self, address):
        """
        Determina el tipo de dato asociado a una dirección virtual.

        Args:
            address (int): La dirección virtual

        Returns:
            Type: El tipo de dato asociado a la dirección
        """
        # Determinar el segmento (global, local, temp, const)
        if self.global_base <= address < self.local_base:
            base = self.global_base
        elif self.local_base <= address < self.temp_base:
            base = self.local_base
        elif self.temp_base <= address < self.const_base:
            base = self.temp_base
        elif self.const_base <= address:
            base = self.const_base
        else:
            return Type.ERROR

        # Calcular el offset dentro del segmento
        offset = address - base

        # Determinar el tipo basado en el offset
        if 0 <= offset < self.type_segment_size:
            return Type.INT
        elif self.type_segment_size <= offset < self.type_segment_size * 2:
            return Type.FLOAT
        elif self.type_segment_size * 2 <= offset < self.type_segment_size * 3:
            return Type.STRING
        elif self.type_segment_size * 3 <= offset < self.type_segment_size * 4:
            return Type.VOID  # Para booleanos y otros tipos especiales
        else:
            return Type.ERROR

class Quadruple:
    """Representa un cuádruplo en el código intermedio."""

    def __init__(self, operator, left_operand, right_operand, result):
        self.operator = operator
        self.left_operand = left_operand
        self.right_operand = right_operand
        self.result = result

    def __str__(self):
        return f"({self.operator}, {self.left_operand}, {self.right_operand}, {self.result})"

    def __repr__(self):
        return self.__str__()

class CodeGenerator:
    """Generador de código intermedio para BabyDuck."""

    def __init__(self, function_directory, semantic_cube):
        # Pilas para generación de cuádruplos
        self.operand_stack = []  # Pila de operandos (direcciones)
        self.operator_stack = []  # Pila de operadores
        self.type_stack = []     # Pila de tipos
        self.jump_stack = []     # Pila de saltos

        # Fila de cuádruplos
        self.quadruples = deque()

        # Contador de cuádruplos
        self.quad_counter = 0

        # Referencias a estructuras del analizador semántico
        self.function_directory = function_directory
        self.semantic_cube = semantic_cube

        # Administrador de memoria
        self.memory = MemoryManager()

        # Tabla temporal para mapear variables a direcciones
        self.var_address_map = {}

        # Función actual para contexto
        self.current_function = None

        # Mapa de operadores de ANTLR a operadores de cuádruplos
        self.operator_map = {
            Operator.PLUS: QuadrupleType.PLUS,
            Operator.MINUS: QuadrupleType.MINUS,
            Operator.MULT: QuadrupleType.MULT,
            Operator.DIV: QuadrupleType.DIV,
            Operator.ASSIGN: QuadrupleType.ASSIGN,
            Operator.GT: QuadrupleType.GREATER_THAN,
            Operator.LT: QuadrupleType.LESS_THAN,
            Operator.GE: QuadrupleType.GREATER_EQUAL,
            Operator.LE: QuadrupleType.LESS_EQUAL,
            Operator.EQ: QuadrupleType.EQUAL,
            Operator.NEQ: QuadrupleType.NOT_EQUAL,
            Operator.AND: QuadrupleType.AND,
            Operator.OR: QuadrupleType.OR
        }

    def push_operand(self, operand, operand_type):
        """Agrega un operando y su tipo a las pilas correspondientes."""
        self.operand_stack.append(operand)
        self.type_stack.append(operand_type)

    def pop_operand(self):
        """Extrae un operando y su tipo de las pilas."""
        if not self.operand_stack or not self.type_stack:
            return None, None
        return self.operand_stack.pop(), self.type_stack.pop()

    def push_operator(self, operator):
        """Agrega un operador a la pila de operadores."""
        self.operator_stack.append(operator)

    def pop_operator(self):
        """Extrae un operador de la pila de operadores."""
        if not self.operator_stack:
            return None
        return self.operator_stack.pop()

    def top_operator(self):
        """Obtiene el operador en el tope de la pila sin extraerlo."""
        if not self.operator_stack:
            return None
        return self.operator_stack[-1]

    def push_jump(self, jump):
        """Agrega un salto a la pila de saltos."""
        self.jump_stack.append(jump)

    def pop_jump(self):
        """Extrae un salto de la pila de saltos."""
        if not self.jump_stack:
            return None
        return self.jump_stack.pop()

    def generate_quadruple(self, operator, left_operand, right_operand, result):
        """Genera un cuádruplo y lo agrega a la fila."""
        quad = Quadruple(operator, left_operand, right_operand, result)
        self.quadruples.append(quad)
        self.quad_counter += 1
        return self.quad_counter - 1  # Retorna el índice del cuádruplo

    def get_address_for_variable(self, var_name):
        """Obtiene la dirección de memoria para una variable."""
        # Create a scope-aware key for the variable
        scope_key = f"{self.current_function or 'global'}:{var_name}"

        if scope_key in self.var_address_map:
            return self.var_address_map[scope_key]

        # Buscar en la tabla de variables
        var_entry = self.function_directory.lookup_variable(var_name)
        if not var_entry:
            # Error: variable no definida
            return None

        # Determinar si es global o local
        is_global = True

        # Check if we're in a function context
        if self.current_function is not None:
            # Check if the variable is in the current function's variable table
            func = self.function_directory.functions.get(self.current_function)
            if func and var_name in func.var_table.vars:
                is_global = False

        if is_global:
            address = self.memory.get_global_address(var_entry.type)
        else:
            address = self.memory.get_local_address(var_entry.type)

        self.var_address_map[scope_key] = address
        return address

    def get_temp_address(self, result_type):
        """Obtiene una dirección temporal para un resultado."""
        return self.memory.get_temp_address(result_type)

    def generate_expression(self):
        """Genera cuádruplos para una expresión."""
        # Procesar operadores de la pila según precedencia
        if not self.operator_stack:
            return

        # Operadores aritméticos y relacionales
        op = self.top_operator()
        if op in [QuadrupleType.PLUS, QuadrupleType.MINUS, QuadrupleType.MULT,
                  QuadrupleType.DIV, QuadrupleType.GREATER_THAN,
                  QuadrupleType.LESS_THAN, QuadrupleType.GREATER_EQUAL,
                  QuadrupleType.LESS_EQUAL, QuadrupleType.EQUAL,
                  QuadrupleType.NOT_EQUAL]:

            self.pop_operator()  # Sacar operador

            # Sacar operandos y tipos
            right_operand, right_type = self.pop_operand()
            left_operand, left_type = self.pop_operand()

            # Verificar tipos con cubo semántico
            antlr_op = None
            for key, value in self.operator_map.items():
                if value == op:
                    antlr_op = key
                    break

            if antlr_op:
                result_type = self.semantic_cube.get_result_type(left_type, right_type, antlr_op)
                if result_type == Type.ERROR:
                    # Error: incompatibilidad de tipos
                    print(f"Error: incompatibilidad de tipos en operación {op}")
                    return

                # Generar temporal para resultado
                temp_address = self.get_temp_address(result_type)

                # Generar cuádruplo
                self.generate_quadruple(op, left_operand, right_operand, temp_address)

                # Apilar resultado
                self.push_operand(temp_address, result_type)

    def generate_assignment(self, var_name):
        """Genera cuádruplos para una asignación."""
        # Obtener valor y tipo a asignar
        value, value_type = self.pop_operand()

        # Obtener dirección de la variable destino
        var_address = self.get_address_for_variable(var_name)
        if not var_address:
            print(f"Error: variable no definida: {var_name}")
            return

        # Buscar el tipo de la variable
        var_entry = self.function_directory.lookup_variable(var_name)
        if not var_entry:
            print(f"Error: variable no definida: {var_name}")
            return

        # Verificar compatibilidad de tipos
        antlr_op = Operator.ASSIGN
        result_type = self.semantic_cube.get_result_type(var_entry.type, value_type, antlr_op)
        if result_type == Type.ERROR:
            print(f"Error: incompatibilidad de tipos en asignación a {var_name}")
            return

        # Generar cuádruplo de asignación
        self.generate_quadruple(QuadrupleType.ASSIGN, value, None, var_address)

    def start_if(self):
        """Inicia la generación de código para una condición if."""
        # Obtener la expresión de condición
        condition, condition_type = self.pop_operand()

        # Verificar que sea booleana (en BabyDuck, los enteros se usan para booleanos)
        if condition_type != Type.INT:
            print("Error: condición debe ser de tipo booleano (int)")
            return

        # Generar cuádruplo GOTOF (salto si falso)
        gotof_index = self.generate_quadruple(QuadrupleType.GOTOF, condition, None, None)

        # Apilar salto para rellenar después
        self.push_jump(gotof_index)

    def mid_if(self):
        """Procesa la parte media de un if-else."""
        # Sacar salto pendiente del if
        end_if_jump = self.pop_jump()

        # Generar salto incondicional para saltar el else
        goto_index = self.generate_quadruple(QuadrupleType.GOTO, None, None, None)

        # Rellenar el GOTOF con la dirección actual (inicio del else)
        self.quadruples[end_if_jump].result = self.quad_counter

        # Apilar salto para el end
        self.push_jump(goto_index)

    def end_if(self):
        """Finaliza la generación de código para una condición if/if-else."""
        # Sacar el último salto pendiente
        last_jump = self.pop_jump()

        # Rellenar con la dirección actual
        self.quadruples[last_jump].result = self.quad_counter

    def start_while(self):
        """Inicia la generación de código para un ciclo while."""
        # Guardar posición de inicio del ciclo
        self.push_jump(self.quad_counter)

    def mid_while(self):
        """Procesa la condición de un ciclo while."""
        # Obtener la expresión de condición
        condition, condition_type = self.pop_operand()

        # Verificar que sea booleana
        if condition_type != Type.INT:
            print("Error: condición debe ser de tipo booleano (int)")
            return

        # Generar cuádruplo GOTOF (salto si falso)
        gotof_index = self.generate_quadruple(QuadrupleType.GOTOF, condition, None, None)

        # Apilar salto para rellenar después
        self.push_jump(gotof_index)

    def end_while(self):
        """Finaliza la generación de código para un ciclo while."""
        # Sacar salto de GOTOF
        gotof_jump = self.pop_jump()

        # Sacar posición de inicio del ciclo
        start_pos = self.pop_jump()

        # Generar salto incondicional al inicio del ciclo
        self.generate_quadruple(QuadrupleType.GOTO, None, None, start_pos)

        # Rellenar el GOTOF con la dirección actual (fin del ciclo)
        self.quadruples[gotof_jump].result = self.quad_counter

    def generate_print(self, is_string=False, string_value=None):
        """Genera cuádruplos para imprimir."""
        if is_string:
            # Es una cadena literal
            # Almacenar la cadena constante y obtener su dirección
            string_address = self.memory.get_constant_address(string_value, Type.STRING)
            self.generate_quadruple(QuadrupleType.PRINT, None, None, string_address)
        else:
            # Es una expresión
            value, _ = self.pop_operand()
            self.generate_quadruple(QuadrupleType.PRINT, None, None, value)

    def generate_newline(self):
        """Genera un cuádruplo para salto de línea."""
        self.generate_quadruple(QuadrupleType.NEWLINE, None, None, None)

    def add_constant(self, value, type_val):
        """Agrega una constante a la tabla de constantes y devuelve su dirección."""
        return self.memory.get_constant_address(value, type_val)

    def set_function_context(self, function_name):
        """Establece el contexto de función actual."""
        self.current_function = function_name
        self.memory.reset_local_counters()
        self.memory.reset_temp_counters()

    def end_function(self):
        """Finaliza el contexto de función actual."""
        self.generate_quadruple(QuadrupleType.ENDPROC, None, None, None)
        self.current_function = None

    def end_program(self):
        """Genera el cuádruplo final del programa."""
        self.generate_quadruple(QuadrupleType.END, None, None, None)

    def print_quadruples(self):
        """Imprime todos los cuádruplos generados."""
        print("\n===== QUADRUPLES =====")
        for i, quad in enumerate(self.quadruples):
            print(f"{i}: {quad}")

    def save_quadruples(self, filename):
        """Guarda los cuádruplos en un archivo."""
        with open(filename, 'w') as f:
            f.write("===== QUADRUPLES =====\n")
            for i, quad in enumerate(self.quadruples):
                f.write(f"{i}: {quad}\n")

    def get_constant_table(self):
        """Retorna la tabla de constantes para la máquina virtual."""
        return self.memory.constant_table