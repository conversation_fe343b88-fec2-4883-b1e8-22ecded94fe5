===== QUADRUPLES =====
0: (QuadrupleType.GOTO, None, None, 17)
1: (QuadrupleType.ASSIGN, 13000, None, 5000)
2: (QuadrupleType.ASSIGN, 13000, None, 5001)
3: (QuadrupleType.PLUS, 5002, 13000, 9000)
4: (QuadrupleType.LESS_THAN, 5001, 9000, 9001)
5: (QuadrupleType.GOTOF, 9001, None, 11)
6: (QuadrupleType.MULT, 5000, 5001, 9002)
7: (QuadrupleType.ASSIGN, 9002, None, 5000)
8: (QuadrupleType.PLUS, 5001, 13000, 9003)
9: (QuadrupleType.ASSIGN, 9003, None, 5001)
10: (QuadrupleType.GOTO, None, None, 3)
11: (QuadrupleType.PRINT, None, None, 15000)
12: (QuadrupleType.PRINT, None, None, 5002)
13: (QuadrupleType.PRINT, None, None, 15001)
14: (QuadrupleType.PRINT, None, None, 5000)
15: (QuadrupleType.<PERSON><PERSON><PERSON><PERSON><PERSON>, None, None, None)
16: (QuadrupleType.END<PERSON><PERSON>, None, None, None)
17: (QuadrupleType.ASSIGN, 13001, None, 1000)
18: (QuadrupleType.ASSIGN, 13002, None, 1001)
19: (QuadrupleType.ASSIGN, 13003, None, 1002)
20: (QuadrupleType.ASSIGN, 14000, None, 2000)
21: (QuadrupleType.ERA, None, None, factorial)
22: (QuadrupleType.PARAM, 1000, None, 0)
23: (QuadrupleType.GOSUB, None, None, 1)
24: (QuadrupleType.PRINT, None, None, 15002)
25: (QuadrupleType.PRINT, None, None, 15003)
26: (QuadrupleType.NEWLINE, None, None, None)
27: (QuadrupleType.END, None, None, None)
