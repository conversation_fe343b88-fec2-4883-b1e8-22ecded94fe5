"""
BabyDuck Program Runner
----------------------
Simple runner to execute compiled BabyDuck programs.
Usage: python run_program.py <quadruple_file> [--debug]

Automatically loads constants from the corresponding .const file.
By default, only shows the program output.
Use --debug flag to see detailed execution trace and constant loading.

Example:
  python run_program.py test_simple.quad        # Run quietly
  python run_program.py test_simple.quad --debug # Run with debug info
"""

import sys
import json
from virtual_machine import VirtualMachine

def load_constants_from_file(constants_file):
    """Load constants from .const file generated by compiler."""
    try:
        with open(constants_file, 'r') as f:
            constant_table = json.load(f)

        # Convert string keys back to appropriate types and values
        constants = {}
        for value_str, address in constant_table.items():
            # Try to convert value back to appropriate type
            try:
                # Try integer first
                value = int(value_str)
            except ValueError:
                try:
                    # Try float
                    value = float(value_str)
                except ValueError:
                    # Keep as string
                    value = value_str

            constants[address] = value

        return constants
    except Exception as e:
        print(f"Warning: Could not load constants from {constants_file}: {e}")
        return {}

def load_function_info_from_file(function_file):
    """Load function information from .func file generated by compiler."""
    try:
        with open(function_file, 'r') as f:
            function_info = json.load(f)
        return function_info
    except Exception as e:
        print(f"Warning: Could not load function info from {function_file}: {e}")
        return None

def main():
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python run_program.py <quadruple_file> [--debug]")
        sys.exit(1)

    quadruple_file = sys.argv[1]
    debug_mode = len(sys.argv) == 3 and sys.argv[2] == "--debug"

    # Try to find corresponding constants and function files
    base_name = quadruple_file.replace('.quad', '')
    constants_file = base_name + '.const'
    function_file = base_name + '.func'

    if debug_mode:
        print(f"Loading program: {quadruple_file}")

    # Create virtual machine
    vm = VirtualMachine(debug=debug_mode)

    # Load constants if available
    constants = load_constants_from_file(constants_file)

    # Load function information if available
    function_info = load_function_info_from_file(function_file)

    # Load and execute the program
    if vm.load_program(quadruple_file, constants, function_info):
        if debug_mode:
            print("Program loaded successfully")

        # Load constants from the compiler's constant table
        for address, value in constants.items():
            vm.memory.set_value(address, value)
            if debug_mode:
                print(f"Loaded constant: {address} = {value}")

        try:
            vm.execute()
        except KeyboardInterrupt:
            print("\nProgram execution interrupted by user")
        except Exception as e:
            print(f"Runtime error: {e}")
    else:
        print("Failed to load program")
        sys.exit(1)

if __name__ == "__main__":
    main()
