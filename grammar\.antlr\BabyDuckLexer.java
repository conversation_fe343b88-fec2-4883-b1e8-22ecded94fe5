// Generated from c:/Users/<USER>/Desktop/BabyDuckProject/grammar/BabyDuck.g4 by ANTLR 4.13.1
import org.antlr.v4.runtime.Lexer;
import org.antlr.v4.runtime.CharStream;
import org.antlr.v4.runtime.Token;
import org.antlr.v4.runtime.TokenStream;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.atn.*;
import org.antlr.v4.runtime.dfa.DFA;
import org.antlr.v4.runtime.misc.*;

@SuppressWarnings({"all", "warnings", "unchecked", "unused", "cast", "CheckReturnValue", "this-escape"})
public class BabyDuckLexer extends Lexer {
	static { RuntimeMetaData.checkVersion("4.13.1", RuntimeMetaData.VERSION); }

	protected static final DFA[] _decisionToDFA;
	protected static final PredictionContextCache _sharedContextCache =
		new PredictionContextCache();
	public static final int
		PROGRAM=1, MAIN=2, END=3, VAR=4, INT=5, FLOAT=6, VOID=7, IF=8, ELSE=9, 
		WHILE=10, DO=11, PRINT=12, PLUS=13, MINUS=14, MULT=15, DIV=16, EQ=17, 
		AND=18, OR=19, SAME=20, NEQ=21, LT=22, GT=23, LPAREN=24, RPAREN=25, LBRACE=26, 
		RBRACE=27, COLON=28, SEMICOLON=29, COMMA=30, LBRACKET=31, RBRACKET=32, 
		CTE_INT=33, CTE_FLOAT=34, CTE_STRING=35, ID=36, WS=37, LINE_COMMENT=38;
	public static String[] channelNames = {
		"DEFAULT_TOKEN_CHANNEL", "HIDDEN"
	};

	public static String[] modeNames = {
		"DEFAULT_MODE"
	};

	private static String[] makeRuleNames() {
		return new String[] {
			"PROGRAM", "MAIN", "END", "VAR", "INT", "FLOAT", "VOID", "IF", "ELSE", 
			"WHILE", "DO", "PRINT", "PLUS", "MINUS", "MULT", "DIV", "EQ", "AND", 
			"OR", "SAME", "NEQ", "LT", "GT", "LPAREN", "RPAREN", "LBRACE", "RBRACE", 
			"COLON", "SEMICOLON", "COMMA", "LBRACKET", "RBRACKET", "CTE_INT", "CTE_FLOAT", 
			"CTE_STRING", "ID", "WS", "LINE_COMMENT"
		};
	}
	public static final String[] ruleNames = makeRuleNames();

	private static String[] makeLiteralNames() {
		return new String[] {
			null, "'program'", "'main'", "'end'", "'var'", "'int'", "'float'", "'void'", 
			"'if'", "'else'", "'while'", "'do'", "'print'", "'+'", "'-'", "'*'", 
			"'/'", "'='", "'&&'", "'||'", "'=='", "'!='", "'<'", "'>'", "'('", "')'", 
			"'{'", "'}'", "':'", "';'", "','", "'['", "']'"
		};
	}
	private static final String[] _LITERAL_NAMES = makeLiteralNames();
	private static String[] makeSymbolicNames() {
		return new String[] {
			null, "PROGRAM", "MAIN", "END", "VAR", "INT", "FLOAT", "VOID", "IF", 
			"ELSE", "WHILE", "DO", "PRINT", "PLUS", "MINUS", "MULT", "DIV", "EQ", 
			"AND", "OR", "SAME", "NEQ", "LT", "GT", "LPAREN", "RPAREN", "LBRACE", 
			"RBRACE", "COLON", "SEMICOLON", "COMMA", "LBRACKET", "RBRACKET", "CTE_INT", 
			"CTE_FLOAT", "CTE_STRING", "ID", "WS", "LINE_COMMENT"
		};
	}
	private static final String[] _SYMBOLIC_NAMES = makeSymbolicNames();
	public static final Vocabulary VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);

	/**
	 * @deprecated Use {@link #VOCABULARY} instead.
	 */
	@Deprecated
	public static final String[] tokenNames;
	static {
		tokenNames = new String[_SYMBOLIC_NAMES.length];
		for (int i = 0; i < tokenNames.length; i++) {
			tokenNames[i] = VOCABULARY.getLiteralName(i);
			if (tokenNames[i] == null) {
				tokenNames[i] = VOCABULARY.getSymbolicName(i);
			}

			if (tokenNames[i] == null) {
				tokenNames[i] = "<INVALID>";
			}
		}
	}

	@Override
	@Deprecated
	public String[] getTokenNames() {
		return tokenNames;
	}

	@Override

	public Vocabulary getVocabulary() {
		return VOCABULARY;
	}


	public BabyDuckLexer(CharStream input) {
		super(input);
		_interp = new LexerATNSimulator(this,_ATN,_decisionToDFA,_sharedContextCache);
	}

	@Override
	public String getGrammarFileName() { return "BabyDuck.g4"; }

	@Override
	public String[] getRuleNames() { return ruleNames; }

	@Override
	public String getSerializedATN() { return _serializedATN; }

	@Override
	public String[] getChannelNames() { return channelNames; }

	@Override
	public String[] getModeNames() { return modeNames; }

	@Override
	public ATN getATN() { return _ATN; }

	public static final String _serializedATN =
		"\u0004\u0000&\u00e6\u0006\uffff\uffff\u0002\u0000\u0007\u0000\u0002\u0001"+
		"\u0007\u0001\u0002\u0002\u0007\u0002\u0002\u0003\u0007\u0003\u0002\u0004"+
		"\u0007\u0004\u0002\u0005\u0007\u0005\u0002\u0006\u0007\u0006\u0002\u0007"+
		"\u0007\u0007\u0002\b\u0007\b\u0002\t\u0007\t\u0002\n\u0007\n\u0002\u000b"+
		"\u0007\u000b\u0002\f\u0007\f\u0002\r\u0007\r\u0002\u000e\u0007\u000e\u0002"+
		"\u000f\u0007\u000f\u0002\u0010\u0007\u0010\u0002\u0011\u0007\u0011\u0002"+
		"\u0012\u0007\u0012\u0002\u0013\u0007\u0013\u0002\u0014\u0007\u0014\u0002"+
		"\u0015\u0007\u0015\u0002\u0016\u0007\u0016\u0002\u0017\u0007\u0017\u0002"+
		"\u0018\u0007\u0018\u0002\u0019\u0007\u0019\u0002\u001a\u0007\u001a\u0002"+
		"\u001b\u0007\u001b\u0002\u001c\u0007\u001c\u0002\u001d\u0007\u001d\u0002"+
		"\u001e\u0007\u001e\u0002\u001f\u0007\u001f\u0002 \u0007 \u0002!\u0007"+
		"!\u0002\"\u0007\"\u0002#\u0007#\u0002$\u0007$\u0002%\u0007%\u0001\u0000"+
		"\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000"+
		"\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001"+
		"\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0003\u0001\u0003"+
		"\u0001\u0003\u0001\u0003\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004"+
		"\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005"+
		"\u0001\u0006\u0001\u0006\u0001\u0006\u0001\u0006\u0001\u0006\u0001\u0007"+
		"\u0001\u0007\u0001\u0007\u0001\b\u0001\b\u0001\b\u0001\b\u0001\b\u0001"+
		"\t\u0001\t\u0001\t\u0001\t\u0001\t\u0001\t\u0001\n\u0001\n\u0001\n\u0001"+
		"\u000b\u0001\u000b\u0001\u000b\u0001\u000b\u0001\u000b\u0001\u000b\u0001"+
		"\f\u0001\f\u0001\r\u0001\r\u0001\u000e\u0001\u000e\u0001\u000f\u0001\u000f"+
		"\u0001\u0010\u0001\u0010\u0001\u0011\u0001\u0011\u0001\u0011\u0001\u0012"+
		"\u0001\u0012\u0001\u0012\u0001\u0013\u0001\u0013\u0001\u0013\u0001\u0014"+
		"\u0001\u0014\u0001\u0014\u0001\u0015\u0001\u0015\u0001\u0016\u0001\u0016"+
		"\u0001\u0017\u0001\u0017\u0001\u0018\u0001\u0018\u0001\u0019\u0001\u0019"+
		"\u0001\u001a\u0001\u001a\u0001\u001b\u0001\u001b\u0001\u001c\u0001\u001c"+
		"\u0001\u001d\u0001\u001d\u0001\u001e\u0001\u001e\u0001\u001f\u0001\u001f"+
		"\u0001 \u0004 \u00b6\b \u000b \f \u00b7\u0001!\u0004!\u00bb\b!\u000b!"+
		"\f!\u00bc\u0001!\u0001!\u0004!\u00c1\b!\u000b!\f!\u00c2\u0001\"\u0001"+
		"\"\u0005\"\u00c7\b\"\n\"\f\"\u00ca\t\"\u0001\"\u0001\"\u0001#\u0001#\u0005"+
		"#\u00d0\b#\n#\f#\u00d3\t#\u0001$\u0004$\u00d6\b$\u000b$\f$\u00d7\u0001"+
		"$\u0001$\u0001%\u0001%\u0001%\u0001%\u0005%\u00e0\b%\n%\f%\u00e3\t%\u0001"+
		"%\u0001%\u0001\u00c8\u0000&\u0001\u0001\u0003\u0002\u0005\u0003\u0007"+
		"\u0004\t\u0005\u000b\u0006\r\u0007\u000f\b\u0011\t\u0013\n\u0015\u000b"+
		"\u0017\f\u0019\r\u001b\u000e\u001d\u000f\u001f\u0010!\u0011#\u0012%\u0013"+
		"\'\u0014)\u0015+\u0016-\u0017/\u00181\u00193\u001a5\u001b7\u001c9\u001d"+
		";\u001e=\u001f? A!C\"E#G$I%K&\u0001\u0000\u0005\u0001\u000009\u0003\u0000"+
		"AZ__az\u0004\u000009AZ__az\u0003\u0000\t\n\r\r  \u0002\u0000\n\n\r\r\u00ec"+
		"\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0003\u0001\u0000\u0000\u0000"+
		"\u0000\u0005\u0001\u0000\u0000\u0000\u0000\u0007\u0001\u0000\u0000\u0000"+
		"\u0000\t\u0001\u0000\u0000\u0000\u0000\u000b\u0001\u0000\u0000\u0000\u0000"+
		"\r\u0001\u0000\u0000\u0000\u0000\u000f\u0001\u0000\u0000\u0000\u0000\u0011"+
		"\u0001\u0000\u0000\u0000\u0000\u0013\u0001\u0000\u0000\u0000\u0000\u0015"+
		"\u0001\u0000\u0000\u0000\u0000\u0017\u0001\u0000\u0000\u0000\u0000\u0019"+
		"\u0001\u0000\u0000\u0000\u0000\u001b\u0001\u0000\u0000\u0000\u0000\u001d"+
		"\u0001\u0000\u0000\u0000\u0000\u001f\u0001\u0000\u0000\u0000\u0000!\u0001"+
		"\u0000\u0000\u0000\u0000#\u0001\u0000\u0000\u0000\u0000%\u0001\u0000\u0000"+
		"\u0000\u0000\'\u0001\u0000\u0000\u0000\u0000)\u0001\u0000\u0000\u0000"+
		"\u0000+\u0001\u0000\u0000\u0000\u0000-\u0001\u0000\u0000\u0000\u0000/"+
		"\u0001\u0000\u0000\u0000\u00001\u0001\u0000\u0000\u0000\u00003\u0001\u0000"+
		"\u0000\u0000\u00005\u0001\u0000\u0000\u0000\u00007\u0001\u0000\u0000\u0000"+
		"\u00009\u0001\u0000\u0000\u0000\u0000;\u0001\u0000\u0000\u0000\u0000="+
		"\u0001\u0000\u0000\u0000\u0000?\u0001\u0000\u0000\u0000\u0000A\u0001\u0000"+
		"\u0000\u0000\u0000C\u0001\u0000\u0000\u0000\u0000E\u0001\u0000\u0000\u0000"+
		"\u0000G\u0001\u0000\u0000\u0000\u0000I\u0001\u0000\u0000\u0000\u0000K"+
		"\u0001\u0000\u0000\u0000\u0001M\u0001\u0000\u0000\u0000\u0003U\u0001\u0000"+
		"\u0000\u0000\u0005Z\u0001\u0000\u0000\u0000\u0007^\u0001\u0000\u0000\u0000"+
		"\tb\u0001\u0000\u0000\u0000\u000bf\u0001\u0000\u0000\u0000\rl\u0001\u0000"+
		"\u0000\u0000\u000fq\u0001\u0000\u0000\u0000\u0011t\u0001\u0000\u0000\u0000"+
		"\u0013y\u0001\u0000\u0000\u0000\u0015\u007f\u0001\u0000\u0000\u0000\u0017"+
		"\u0082\u0001\u0000\u0000\u0000\u0019\u0088\u0001\u0000\u0000\u0000\u001b"+
		"\u008a\u0001\u0000\u0000\u0000\u001d\u008c\u0001\u0000\u0000\u0000\u001f"+
		"\u008e\u0001\u0000\u0000\u0000!\u0090\u0001\u0000\u0000\u0000#\u0092\u0001"+
		"\u0000\u0000\u0000%\u0095\u0001\u0000\u0000\u0000\'\u0098\u0001\u0000"+
		"\u0000\u0000)\u009b\u0001\u0000\u0000\u0000+\u009e\u0001\u0000\u0000\u0000"+
		"-\u00a0\u0001\u0000\u0000\u0000/\u00a2\u0001\u0000\u0000\u00001\u00a4"+
		"\u0001\u0000\u0000\u00003\u00a6\u0001\u0000\u0000\u00005\u00a8\u0001\u0000"+
		"\u0000\u00007\u00aa\u0001\u0000\u0000\u00009\u00ac\u0001\u0000\u0000\u0000"+
		";\u00ae\u0001\u0000\u0000\u0000=\u00b0\u0001\u0000\u0000\u0000?\u00b2"+
		"\u0001\u0000\u0000\u0000A\u00b5\u0001\u0000\u0000\u0000C\u00ba\u0001\u0000"+
		"\u0000\u0000E\u00c4\u0001\u0000\u0000\u0000G\u00cd\u0001\u0000\u0000\u0000"+
		"I\u00d5\u0001\u0000\u0000\u0000K\u00db\u0001\u0000\u0000\u0000MN\u0005"+
		"p\u0000\u0000NO\u0005r\u0000\u0000OP\u0005o\u0000\u0000PQ\u0005g\u0000"+
		"\u0000QR\u0005r\u0000\u0000RS\u0005a\u0000\u0000ST\u0005m\u0000\u0000"+
		"T\u0002\u0001\u0000\u0000\u0000UV\u0005m\u0000\u0000VW\u0005a\u0000\u0000"+
		"WX\u0005i\u0000\u0000XY\u0005n\u0000\u0000Y\u0004\u0001\u0000\u0000\u0000"+
		"Z[\u0005e\u0000\u0000[\\\u0005n\u0000\u0000\\]\u0005d\u0000\u0000]\u0006"+
		"\u0001\u0000\u0000\u0000^_\u0005v\u0000\u0000_`\u0005a\u0000\u0000`a\u0005"+
		"r\u0000\u0000a\b\u0001\u0000\u0000\u0000bc\u0005i\u0000\u0000cd\u0005"+
		"n\u0000\u0000de\u0005t\u0000\u0000e\n\u0001\u0000\u0000\u0000fg\u0005"+
		"f\u0000\u0000gh\u0005l\u0000\u0000hi\u0005o\u0000\u0000ij\u0005a\u0000"+
		"\u0000jk\u0005t\u0000\u0000k\f\u0001\u0000\u0000\u0000lm\u0005v\u0000"+
		"\u0000mn\u0005o\u0000\u0000no\u0005i\u0000\u0000op\u0005d\u0000\u0000"+
		"p\u000e\u0001\u0000\u0000\u0000qr\u0005i\u0000\u0000rs\u0005f\u0000\u0000"+
		"s\u0010\u0001\u0000\u0000\u0000tu\u0005e\u0000\u0000uv\u0005l\u0000\u0000"+
		"vw\u0005s\u0000\u0000wx\u0005e\u0000\u0000x\u0012\u0001\u0000\u0000\u0000"+
		"yz\u0005w\u0000\u0000z{\u0005h\u0000\u0000{|\u0005i\u0000\u0000|}\u0005"+
		"l\u0000\u0000}~\u0005e\u0000\u0000~\u0014\u0001\u0000\u0000\u0000\u007f"+
		"\u0080\u0005d\u0000\u0000\u0080\u0081\u0005o\u0000\u0000\u0081\u0016\u0001"+
		"\u0000\u0000\u0000\u0082\u0083\u0005p\u0000\u0000\u0083\u0084\u0005r\u0000"+
		"\u0000\u0084\u0085\u0005i\u0000\u0000\u0085\u0086\u0005n\u0000\u0000\u0086"+
		"\u0087\u0005t\u0000\u0000\u0087\u0018\u0001\u0000\u0000\u0000\u0088\u0089"+
		"\u0005+\u0000\u0000\u0089\u001a\u0001\u0000\u0000\u0000\u008a\u008b\u0005"+
		"-\u0000\u0000\u008b\u001c\u0001\u0000\u0000\u0000\u008c\u008d\u0005*\u0000"+
		"\u0000\u008d\u001e\u0001\u0000\u0000\u0000\u008e\u008f\u0005/\u0000\u0000"+
		"\u008f \u0001\u0000\u0000\u0000\u0090\u0091\u0005=\u0000\u0000\u0091\""+
		"\u0001\u0000\u0000\u0000\u0092\u0093\u0005&\u0000\u0000\u0093\u0094\u0005"+
		"&\u0000\u0000\u0094$\u0001\u0000\u0000\u0000\u0095\u0096\u0005|\u0000"+
		"\u0000\u0096\u0097\u0005|\u0000\u0000\u0097&\u0001\u0000\u0000\u0000\u0098"+
		"\u0099\u0005=\u0000\u0000\u0099\u009a\u0005=\u0000\u0000\u009a(\u0001"+
		"\u0000\u0000\u0000\u009b\u009c\u0005!\u0000\u0000\u009c\u009d\u0005=\u0000"+
		"\u0000\u009d*\u0001\u0000\u0000\u0000\u009e\u009f\u0005<\u0000\u0000\u009f"+
		",\u0001\u0000\u0000\u0000\u00a0\u00a1\u0005>\u0000\u0000\u00a1.\u0001"+
		"\u0000\u0000\u0000\u00a2\u00a3\u0005(\u0000\u0000\u00a30\u0001\u0000\u0000"+
		"\u0000\u00a4\u00a5\u0005)\u0000\u0000\u00a52\u0001\u0000\u0000\u0000\u00a6"+
		"\u00a7\u0005{\u0000\u0000\u00a74\u0001\u0000\u0000\u0000\u00a8\u00a9\u0005"+
		"}\u0000\u0000\u00a96\u0001\u0000\u0000\u0000\u00aa\u00ab\u0005:\u0000"+
		"\u0000\u00ab8\u0001\u0000\u0000\u0000\u00ac\u00ad\u0005;\u0000\u0000\u00ad"+
		":\u0001\u0000\u0000\u0000\u00ae\u00af\u0005,\u0000\u0000\u00af<\u0001"+
		"\u0000\u0000\u0000\u00b0\u00b1\u0005[\u0000\u0000\u00b1>\u0001\u0000\u0000"+
		"\u0000\u00b2\u00b3\u0005]\u0000\u0000\u00b3@\u0001\u0000\u0000\u0000\u00b4"+
		"\u00b6\u0007\u0000\u0000\u0000\u00b5\u00b4\u0001\u0000\u0000\u0000\u00b6"+
		"\u00b7\u0001\u0000\u0000\u0000\u00b7\u00b5\u0001\u0000\u0000\u0000\u00b7"+
		"\u00b8\u0001\u0000\u0000\u0000\u00b8B\u0001\u0000\u0000\u0000\u00b9\u00bb"+
		"\u0007\u0000\u0000\u0000\u00ba\u00b9\u0001\u0000\u0000\u0000\u00bb\u00bc"+
		"\u0001\u0000\u0000\u0000\u00bc\u00ba\u0001\u0000\u0000\u0000\u00bc\u00bd"+
		"\u0001\u0000\u0000\u0000\u00bd\u00be\u0001\u0000\u0000\u0000\u00be\u00c0"+
		"\u0005.\u0000\u0000\u00bf\u00c1\u0007\u0000\u0000\u0000\u00c0\u00bf\u0001"+
		"\u0000\u0000\u0000\u00c1\u00c2\u0001\u0000\u0000\u0000\u00c2\u00c0\u0001"+
		"\u0000\u0000\u0000\u00c2\u00c3\u0001\u0000\u0000\u0000\u00c3D\u0001\u0000"+
		"\u0000\u0000\u00c4\u00c8\u0005\"\u0000\u0000\u00c5\u00c7\t\u0000\u0000"+
		"\u0000\u00c6\u00c5\u0001\u0000\u0000\u0000\u00c7\u00ca\u0001\u0000\u0000"+
		"\u0000\u00c8\u00c9\u0001\u0000\u0000\u0000\u00c8\u00c6\u0001\u0000\u0000"+
		"\u0000\u00c9\u00cb\u0001\u0000\u0000\u0000\u00ca\u00c8\u0001\u0000\u0000"+
		"\u0000\u00cb\u00cc\u0005\"\u0000\u0000\u00ccF\u0001\u0000\u0000\u0000"+
		"\u00cd\u00d1\u0007\u0001\u0000\u0000\u00ce\u00d0\u0007\u0002\u0000\u0000"+
		"\u00cf\u00ce\u0001\u0000\u0000\u0000\u00d0\u00d3\u0001\u0000\u0000\u0000"+
		"\u00d1\u00cf\u0001\u0000\u0000\u0000\u00d1\u00d2\u0001\u0000\u0000\u0000"+
		"\u00d2H\u0001\u0000\u0000\u0000\u00d3\u00d1\u0001\u0000\u0000\u0000\u00d4"+
		"\u00d6\u0007\u0003\u0000\u0000\u00d5\u00d4\u0001\u0000\u0000\u0000\u00d6"+
		"\u00d7\u0001\u0000\u0000\u0000\u00d7\u00d5\u0001\u0000\u0000\u0000\u00d7"+
		"\u00d8\u0001\u0000\u0000\u0000\u00d8\u00d9\u0001\u0000\u0000\u0000\u00d9"+
		"\u00da\u0006$\u0000\u0000\u00daJ\u0001\u0000\u0000\u0000\u00db\u00dc\u0005"+
		"/\u0000\u0000\u00dc\u00dd\u0005/\u0000\u0000\u00dd\u00e1\u0001\u0000\u0000"+
		"\u0000\u00de\u00e0\b\u0004\u0000\u0000\u00df\u00de\u0001\u0000\u0000\u0000"+
		"\u00e0\u00e3\u0001\u0000\u0000\u0000\u00e1\u00df\u0001\u0000\u0000\u0000"+
		"\u00e1\u00e2\u0001\u0000\u0000\u0000\u00e2\u00e4\u0001\u0000\u0000\u0000"+
		"\u00e3\u00e1\u0001\u0000\u0000\u0000\u00e4\u00e5\u0006%\u0000\u0000\u00e5"+
		"L\u0001\u0000\u0000\u0000\b\u0000\u00b7\u00bc\u00c2\u00c8\u00d1\u00d7"+
		"\u00e1\u0001\u0006\u0000\u0000";
	public static final ATN _ATN =
		new ATNDeserializer().deserialize(_serializedATN.toCharArray());
	static {
		_decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];
		for (int i = 0; i < _ATN.getNumberOfDecisions(); i++) {
			_decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
		}
	}
}