program function_calls;
var 
    result, num : int;

void calculate_square(x: int) [
    var squared: int; {
    squared = x * x;
    print("Square of ", x, " is ", squared);
}];

void math_operations(value: int) [
    var doubled: int; {
    doubled = value * 2;
    print("Double of ", value, " is ", doubled);
    
    print("Now calculating square...");
    calculate_square(doubled);
    
    print("Operations completed!");
}];

main {
    num = 5;
    print("Starting with number: ", num);
    
    math_operations(num);
    
    print("Program finished");
}
end