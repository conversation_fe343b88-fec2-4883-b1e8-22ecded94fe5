"""
Analizador de semantica para babyduck
--------------------------
Funciones que cumple:
1. Cubo semantico para type checking
2. Directorio de funciones
3. Tablas de variables
4. Manejo de errores para validacion semantica
"""
from enum import Enum

# Define tipos
class Type(Enum):
    INT = 1
    FLOAT = 2
    VOID = 3
    STRING = 4
    ERROR = 5

# Define operadores
class Operator(Enum):
    PLUS = '+'
    MINUS = '-'
    MULT = '*'
    DIV = '/'
    ASSIGN = '='
    EQ = '=='  # Para comparacion (usamos == internamente)
    NEQ = '!='
    LT = '<'
    GT = '>'
    LE = '<='
    GE = '>='
    AND = '&&'
    OR = '||'

#Cubo Semantico: Define operaciones validas entre tipos
# Resultado de operacion:[left_type][right_type][operator]
class SemanticCube:
    """
    Semantic Cube (Cubo Semántico) para lenguaje BabyDuck.

    Tabla 3D de lookup Que define el resultado de una operacion entre tipos. Se usa durante el analisis semantico para validacion de tipos, asignaciones, operaciones.

    Estructura:
        cube[left_operand_type][right_operand_type][operator] = result_type

    Si la operacion no es valida (semanticamente), retorna Type.ERROR.
    """
    def __init__(self):
        # Inicializamos el cubo en error para toda operacion
        self.cube = {
            type1: {
                type2: {op: Type.ERROR for op in Operator}
                for type2 in Type
            }
            for type1 in Type
        }

        # Llenamos las operaciones validas

        # Operaciones aritmeticas para INT
        self.cube[Type.INT][Type.INT][Operator.PLUS] = Type.INT
        self.cube[Type.INT][Type.INT][Operator.MINUS] = Type.INT
        self.cube[Type.INT][Type.INT][Operator.MULT] = Type.INT
        self.cube[Type.INT][Type.INT][Operator.DIV] = Type.FLOAT  # Division siempre da FLOAT

        self.cube[Type.INT][Type.FLOAT][Operator.PLUS] = Type.FLOAT
        self.cube[Type.INT][Type.FLOAT][Operator.MINUS] = Type.FLOAT
        self.cube[Type.INT][Type.FLOAT][Operator.MULT] = Type.FLOAT
        self.cube[Type.INT][Type.FLOAT][Operator.DIV] = Type.FLOAT

        # Aritmeticas para FLOAT
        self.cube[Type.FLOAT][Type.INT][Operator.PLUS] = Type.FLOAT
        self.cube[Type.FLOAT][Type.INT][Operator.MINUS] = Type.FLOAT
        self.cube[Type.FLOAT][Type.INT][Operator.MULT] = Type.FLOAT
        self.cube[Type.FLOAT][Type.INT][Operator.DIV] = Type.FLOAT

        self.cube[Type.FLOAT][Type.FLOAT][Operator.PLUS] = Type.FLOAT
        self.cube[Type.FLOAT][Type.FLOAT][Operator.MINUS] = Type.FLOAT
        self.cube[Type.FLOAT][Type.FLOAT][Operator.MULT] = Type.FLOAT
        self.cube[Type.FLOAT][Type.FLOAT][Operator.DIV] = Type.FLOAT

        # Asignacion
        self.cube[Type.INT][Type.INT][Operator.ASSIGN] = Type.INT
        self.cube[Type.FLOAT][Type.FLOAT][Operator.ASSIGN] = Type.FLOAT
        self.cube[Type.FLOAT][Type.INT][Operator.ASSIGN] = Type.FLOAT  # INT se le puede asignar a un FLOAT

        # Operaciones de string
        self.cube[Type.STRING][Type.STRING][Operator.PLUS] = Type.STRING  # Concatenacion de string

        # Comparison
        for type1 in [Type.INT, Type.FLOAT]:
            for type2 in [Type.INT, Type.FLOAT]:
                for op in [Operator.EQ, Operator.NEQ, Operator.LT, Operator.GT, Operator.LE, Operator.GE, Operator.AND, Operator.OR]:
                    self.cube[type1][type2][op] = Type.INT  # Comparisons return INT (as boolean)

        # String comparisons (only equality and inequality)
        self.cube[Type.STRING][Type.STRING][Operator.EQ] = Type.INT
        self.cube[Type.STRING][Type.STRING][Operator.NEQ] = Type.INT

    def get_result_type(self, left_type, right_type, operator):
        """
        Obten el tipo resultante de una operacion entre dos tipos.

        Args:
            left_type (Type): Tipo del operando izquierdo
            right_type (Type): Tipo del operando derecho
            operator (Operator): Operador de la operacion

        Returns:
            Type: Tipo resultante de la operacion, o Type.ERROR si no es valida
        """
        try:
            return self.cube[left_type][right_type][operator]
        except KeyError:
            return Type.ERROR

    def is_operation_valid(self, left_type, right_type, operator):
        """
        Revisa si una operacion entre dos tipos es valida.

        Args:
            left_type (Type): Tipo del operando izquierdo
            right_type (Type): Tipo del operando derecho
            operator (Operator): Operador de la operacion

        Returns:
            Type: Tipo resultante de la operacion, o Type.ERROR si no es valida
        """
        return self.get_result_type(left_type, right_type, operator) != Type.ERROR

# Entrada de variables para symbol tables
class VarEntry:
    """
    Representa la entrada de variables para la tabla de simbolos.

    Almacena informacion sobre una variable, incluyendo su nombre,
    tipo de dato, direccion de memoria, dimensiones (para arreglos),
    y estado de inicializacion.
    """
    def __init__(self, name, type, address=None, dimensions=None):
        self.name = name
        self.type = type
        self.address = address  # Direccion de memoria para generacion de codigo
        self.dimensions = dimensions  # Para arreglos (no implementado aun)
        self.initialized = False  # Lleva cuenta de si ya fue inicializada

    def __repr__(self):
        return f"VarEntry(name='{self.name}', type={self.type}, initialized={self.initialized})"

class ParamEntry:
    """
    Representa una entrada de parametros para la firma de una funcion.

    Almacena informacion sobre un parametro, incluyendo su nombre
    y tipo de dato.
    """
    def __init__(self, name, type):
        self.name = name
        self.type = type

    def __repr__(self):
        return f"ParamEntry(name='{self.name}', type={self.type})"

class FuncEntry:
    """
    Representa una entrada de funciones en el directorio de funciones.

    Almacena informacion sobre una funcion, incluyendo su nombre,
    tipo de retorno, lista de parametros, tabla de variables locales,
    direccion de inicio (para generacion de codigo), y estado de
    declaracion de retorno.
    """
    def __init__(self, name, return_type, params=None, var_table=None, start_address=None):
        self.name = name
        self.return_type = return_type
        self.params = params or []
        self.var_table = var_table or VarTable()
        self.start_address = start_address  # Para generacion de codigo
        self.has_return = False  # Trackea si la funcion tiene una declaracion de retorno
        self.param_count = 0  # Numero de parametros
        self.local_var_count = 0  # Numero de variables locales

    def __repr__(self):
        return f"FuncEntry(name='{self.name}', return_type={self.return_type}, params={self.params})"

class VarTable:
    """
    Maneja variables en un alcance especifico (global o funcion).

    Provee metodos para agregar, buscar, y marcar variables como
    inicializadas, asi como obtener una lista de variables no inicializadas.
    """
    def __init__(self):
        self.vars = {}  # Diccionario de entrada de variables
        self.var_count = 0  # Conteo de variables en esta tabla

    def add(self, name, type):
        """
        Añade una variable a la tabla.

        Args:
            name (str): The variable name
            type (Type): The variable data type

        Returns:
            bool: True si se agrego correctamente, False si ya existe
        """
        if name in self.vars:
            return False  # Variable already exists
        self.vars[name] = VarEntry(name, type)
        self.var_count += 1
        return True

    def lookup(self, name):
        """
        Busca una variable en la tabla.

        Args:
            name (str): The variable name to look up

        Returns:
            VarEntry or None: The variable entry if found, None otherwise
        """
        return self.vars.get(name)

    def mark_initialized(self, name):
        """
       Marca una variable como inicializada.

        Args:
            name (str): La variable a marcar como inicializada

        Returns:
            bool: True si se marco correctamente, False si no se encontro
        """
        var = self.lookup(name)
        if var:
            var.initialized = True
            return True
        return False

    def get_uninitialized_vars(self):
        """
       Obtiene una lista de variables no inicializadas.

        Returns:
            list: Lista de nombres de variables no inicializadas
        """
        return [name for name, var in self.vars.items() if not var.initialized]

    def __repr__(self):
        return f"VarTable(count={self.var_count}, vars={self.vars})"

# Function Directory: Manages functions and their scopes
class FunctionDirectory:
    """
    Maneja funciones y sus alcances en el programa BabyDuck.

    Provee metodos para agregar, buscar, y administrar funciones, parametros,
    y variables, asi como para establecer y resetear el contexto de la
    funcion actual. Es la estructura de datos principal para las funciones
    """
    def __init__(self):
        self.functions = {}  # Dictionary of function entries
        self.global_var_table = VarTable()  # Global variable table
        self.current_function = None  # Current function context

    def add_function(self, name, return_type):
        """
        Args:
            name (str): Nombre de la funcion
            return_type (Type): Tipo de retorno de la funcion

        Returns:
            bool: True si se agrego correctamente, False si ya existe
        """
        if name in self.functions:
            return False  # Function already exists
        self.functions[name] = FuncEntry(name, return_type)
        self.current_function = name
        return True

    def add_param(self, name, type):
        """
        Agrega un parametro a la funcion actual.

        Args:
            name (str): Nombre del parametro
            type (Type): tipo de dato del parametro

        Returns:
            bool: true si se agrego correctamente, false si no hay funcion actual
        """
        if not self.current_function:
            return False
        func = self.functions[self.current_function]
        func.params.append(ParamEntry(name, type))
        # Also add parameter to the function's variable table
        func.var_table.add(name, type)
        func.param_count += 1
        return True

    def add_variable(self, name, type):
        """
        Agrega una variable al alcance actual.

        Args:
            name (str): Nombre de la variable
            type (Type): tipo de dato de la variable

        Returns:
            bool: True si se agrego correctamente, False si ya existe
        """
        if not self.current_function:
            # Add to global scope
            return self.global_var_table.add(name, type)
        else:
            # Add to function scope
            result = self.functions[self.current_function].var_table.add(name, type)
            if result:
                self.functions[self.current_function].local_var_count += 1
            return result

    def lookup_variable(self, name):
        """
        Busca una variable en el alcance actual o global.

        Args:
            name (str): Nombre de la variable a buscar

        Returns:
            VarEntry or None: Entrada de la variable si se encontro, None en caso contrario
        """
        if not self.current_function:
            return self.global_var_table.lookup(name)

        # Check local scope first
        var = self.functions[self.current_function].var_table.lookup(name)
        if var:
            return var

        # If not found, check global scope
        return self.global_var_table.lookup(name)

    def mark_variable_initialized(self, name):
        """
        Marca una variable como inicializada en el alcance actual o global.

        Args:
            name (str): La variable a marcar como inicializada

        Returns:
            bool: True si se marco correctamente, False si no se encontro
        """
        if not self.current_function:
            return self.global_var_table.mark_initialized(name)

        # Try local scope first
        var = self.functions[self.current_function].var_table.lookup(name)
        if var:
            var.initialized = True
            return True

        # If not found, try global scope
        var = self.global_var_table.lookup(name)
        if var:
            var.initialized = True
            return True

        return False

    def lookup_function(self, name):
        """
        Busca una funcion en el directorio.

        Args:
            name (str): Nombre de la funcion a buscar

        Returns:
            FuncEntry or None: La funcion si se encontro, None en caso contrario
        """
        return self.functions.get(name)

    def set_current_function(self, name):
        """
       Pone el contexto de la funcion actual.

        Args:
            name (str): Nombre de la funcion a establecer como actual

        Returns:
            bool: True si se establecio correctamente, False si la funcion no existe
        """
        if name not in self.functions and name is not None:
            return False
        self.current_function = name
        return True

    def reset_current_function(self):
        """Reset to global scope."""
        self.current_function = None

    def mark_function_has_return(self):
        """
        Marca la funcion actual como teniendo una declaracion de retorno.

        Returns:
            bool: True si se marco correctamente, False si no hay funcion actual
        """
        if not self.current_function:
            return False
        self.functions[self.current_function].has_return = True
        return True

    def check_function_return(self, name):
        """
        Verifica si una funcion tiene una declaracion de retorno cuando es necesario.

        Args:
            name (str): Nombre de la funcion a verificar

        Returns:
            bool: True si es valido (void o tiene retorno), False en caso contrario
        """
        func = self.lookup_function(name)
        if not func:
            return False

        # Void functions don't need return statements
        if func.return_type == Type.VOID:
            return True

        # Non-void functions need return statements
        return func.has_return

    def get_uninitialized_vars(self):
        """
        Obtiene una lista de todas las variables no inicializadas.

        Returns:
            dict: Diccionario que mapea alcances a listas de variables no inicializadas
        """
        result = {}

        # Global scope
        global_uninit = self.global_var_table.get_uninitialized_vars()
        if global_uninit:
            result["global"] = global_uninit

        # Function scopes
        for name, func in self.functions.items():
            func_uninit = func.var_table.get_uninitialized_vars()
            if func_uninit:
                result[name] = func_uninit

        return result

    def __repr__(self):
        return f"FunctionDirectory(functions={self.functions}, global_vars={self.global_var_table})"

# SemanticAnalyzer: Main class for semantic analysis
class SemanticAnalyzer:
    """
    Clase principal para el analisis semantico de programas BabyDuck.

    Esta clase coordina el proceso de analisis semantico, usando el cubo semantico
    para verificacion de tipos y el directorio de funciones para gestion de simbolos.
    Provee metodos para validar operaciones, rastrear errores, y generar informacion
    semantica para la generacion de codigo.
    """
    def __init__(self, quiet=False):
        self.semantic_cube = SemanticCube()
        self.function_directory = FunctionDirectory()
        self.errors = []
        self.warnings = []
        self.current_type = None  # For tracking current declaration type
        self.quiet = quiet  # Suppress output when True

    def map_str_to_type(self, type_str):
        """
        Mapea un string a un enum de tipo.

        Args:
            type_str (str): El string de tipo del parser

        Returns:
            Type: El enum de tipo correspondiente
        """
        type_map = {
            "int": Type.INT,
            "float": Type.FLOAT,
            "void": Type.VOID,
            "string": Type.STRING
        }
        return type_map.get(type_str.lower(), Type.ERROR)

    def add_error(self, message):
        """
        Agrega un mensaje de error a la lista de errores.

        Args:
            message (str): El mensaje de error a agregar
        """
        self.errors.append(message)
        print(f"Semantic Error: {message}")

    def add_warning(self, message):
        """
        Agrega un mensaje de advertencia a la lista de advertencias.

        Args:
            message (str): The warning message to add
        """
        self.warnings.append(message)
        if not self.quiet:
            print(f"Semantic Warning: {message}")

    def check_operation(self, left_type, right_type, operator):
        """
        Revisa si una operacion es semanticamente valida.

        Args:
            left_type (Type): The type of the left operand
            right_type (Type): The type of the right operand
            operator (Operator): The operation to perform

        Returns:
            Type: The resulting type of the operation, or Type.ERROR if invalid
        """
        result_type = self.semantic_cube.get_result_type(left_type, right_type, operator)
        if result_type == Type.ERROR:
            self.add_error(f"Type mismatch: cannot perform {operator.value} operation between {left_type} and {right_type}")
        return result_type

    def check_variable_exists(self, name, line_num):
        """
        Revisa si una variable existe en el alcance actual o global.

        Args:
            name (str): The variable name to check
            line_num (int): The line number for error reporting

        Returns:
            VarEntry or None: The variable entry if found, None otherwise
        """
        var = self.function_directory.lookup_variable(name)
        if not var:
            self.add_error(f"Undefined variable '{name}' at line {line_num}")
            return None
        return var

    def check_function_exists(self, name, line_num):
        """
        Revisa si una funcion existe en el directorio de funciones.

        Args:
            name (str): The function name to check
            line_num (int): The line number for error reporting

        Returns:
            FuncEntry or None: The function entry if found, None otherwise
        """
        func = self.function_directory.lookup_function(name)
        if not func:
            self.add_error(f"Undefined function '{name}' at line {line_num}")
            return None
        return func

    def check_function_call_params(self, func_name, param_types, line_num):
        """
        Check if function call parameters match the function signature.

        Args:
            func_name (str): The function name
            param_types (list): List of parameter types in the call
            line_num (int): The line number for error reporting

        Returns:
            bool: True if parameters match, False otherwise
        """
        func = self.function_directory.lookup_function(func_name)
        if not func:
            self.add_error(f"Undefined function '{func_name}' at line {line_num}")
            return False

        # Check parameter count
        if len(param_types) != len(func.params):
            self.add_error(f"Function '{func_name}' called with incorrect number of parameters at line {line_num}. Expected {len(func.params)}, got {len(param_types)}")
            return False

        # Check parameter types
        for i, (param_type, expected_param) in enumerate(zip(param_types, func.params)):
            if param_type != expected_param.type:
                # Special case: INT can be passed to FLOAT parameter
                if param_type == Type.INT and expected_param.type == Type.FLOAT:
                    continue

                self.add_error(f"Type mismatch in parameter {i+1} of function '{func_name}' at line {line_num}. Expected {expected_param.type}, got {param_type}")
                return False

        return True

    def check_variable_initialized(self, name, line_num):
        """
        Check if a variable has been initialized before use.

        Args:
            name (str): The variable name to check
            line_num (int): The line number for error reporting

        Returns:
            bool: True if initialized or warning added, False if variable not found
        """
        var = self.function_directory.lookup_variable(name)
        if not var:
            return False

        if not var.initialized:
            self.add_warning(f"Variable '{name}' may be used before initialization at line {line_num}")
            return True

        return True

    def mark_variable_initialized(self, name):
        """
        Mark a variable as initialized.

        Args:
            name (str): The variable name to mark

        Returns:
            bool: True if marked successfully, False if variable not found
        """
        return self.function_directory.mark_variable_initialized(name)

    def check_duplicate_variable(self, name, line_num):
        """
        Check if a variable name is already used in the current scope.

        Args:
            name (str): The variable name to check
            line_num (int): The line number for error reporting

        Returns:
            bool: True if variable doesn't exist (no duplicate), False otherwise
        """
        # Check in current scope
        if self.function_directory.current_function:
            func = self.function_directory.functions[self.function_directory.current_function]
            if func.var_table.lookup(name):
                self.add_error(f"Duplicate variable '{name}' in function '{self.function_directory.current_function}' at line {line_num}")
                return False
        else:
            # Check in global scope
            if self.function_directory.global_var_table.lookup(name):
                self.add_error(f"Duplicate global variable '{name}' at line {line_num}")
                return False

        return True

    def check_duplicate_function(self, name, line_num):
        """
        Check if a function name is already used.

        Args:
            name (str): The function name to check
            line_num (int): The line number for error reporting

        Returns:
            bool: True if function doesn't exist (no duplicate), False otherwise
        """
        if self.function_directory.lookup_function(name):
            self.add_error(f"Duplicate function '{name}' at line {line_num}")
            return False
        return True

    def validate_return_statements(self):
        """
        Validate that all non-void functions have return statements.

        Returns:
            bool: True if all functions have proper returns, False otherwise
        """
        all_valid = True

        for name, func in self.function_directory.functions.items():
            # Skip void functions and the main program
            if func.return_type == Type.VOID:
                continue

            if not func.has_return:
                self.add_error(f"Function '{name}' with return type {func.return_type} has no return statement")
                all_valid = False

        return all_valid

    def check_uninitialized_variables(self):
        """
        Check for variables that are never initialized.

        This is called at the end of semantic analysis to warn about
        variables that might be used without initialization.
        """
        uninitialized = self.function_directory.get_uninitialized_vars()

        for scope, vars_list in uninitialized.items():
            for var_name in vars_list:
                self.add_warning(f"Variable '{var_name}' in scope '{scope}' may never be initialized")

    def print_tables(self):
        """
        Print function directory and variable tables for debugging.

        This method displays the semantic tables in a readable format,
        including functions, parameters, variables, and any errors or warnings.
        """
        print("\n===== SEMANTIC TABLES =====")
        print("\n----- FUNCTION DIRECTORY -----")
        for name, func in self.function_directory.functions.items():
            print(f"Function: {name}, Return Type: {func.return_type}")
            print(f"  Has Return: {func.has_return}")
            print(f"  Parameter Count: {func.param_count}")
            print(f"  Local Variable Count: {func.local_var_count}")
            print("  Parameters:")
            for param in func.params:
                print(f"    {param.name}: {param.type}")
            print("  Variables:")
            for var_name, var in func.var_table.vars.items():
                print(f"    {var_name}: {var.type} (Initialized: {var.initialized})")

        print("\n----- GLOBAL VARIABLES -----")
        for var_name, var in self.function_directory.global_var_table.vars.items():
            print(f"  {var_name}: {var.type} (Initialized: {var.initialized})")

        if self.errors:
            print("\n----- SEMANTIC ERRORS -----")
            for error in self.errors:
                print(f"  {error}")

        if self.warnings:
            print("\n----- SEMANTIC WARNINGS -----")
            for warning in self.warnings:
                print(f"  {warning}")

    def has_errors(self):
        """
        Check if there are any semantic errors.

        Returns:
            bool: True if there are errors, False otherwise
        """
        return len(self.errors) > 0